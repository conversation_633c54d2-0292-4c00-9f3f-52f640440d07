import React, {useEffect, useRef, useState} from 'react';
import _ from "lodash";

// Types
export type LogStatus = 'error' | 'warn' | 'success' | 'default' | 'debug';

export interface LogEntry {
    id: string;
    title: string;
    message: string;
    time: number; // epoch seconds
    status: LogStatus;
}

interface LogViewerProps {
    logs: LogEntry[];
    height?: string;
    className?: string;
    theme?: 'dark' | 'light';
    lastVisited?: (entry: LogEntry) => void;
}

interface LogItemProps {
    log: LogEntry;
    theme?: 'dark' | 'light';
}

// Helper function to format timestamp
const formatTime = (epochSeconds: number): string => {
    const date = new Date(epochSeconds * 1000);
    return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

// Get status styling
const getStatusStyle = (status: LogStatus, theme: 'dark' | 'light' = 'dark'): string => {
    if (theme === 'light') {
        const statusMap = {
            error: 'text-red-600 border-red-400/40 bg-red-50',
            warn: 'text-yellow-600 border-yellow-400/40 bg-yellow-50',
            success: 'text-green-600 border-green-400/40 bg-green-50',
            debug: 'text-purple-600 border-purple-400/40 bg-purple-50',
            default: 'text-gray-600 border-gray-400/40 bg-gray-50'
        };
        return statusMap[status];
    }

    const statusMap = {
        error: 'text-red-400 border-red-500/30 bg-red-500/5',
        warn: 'text-yellow-400 border-yellow-500/30 bg-yellow-500/5',
        success: 'text-green-400 border-green-500/30 bg-green-500/5',
        debug: 'text-purple-400 border-purple-500/30 bg-purple-500/5',
        default: 'text-gray-300 border-gray-600/30 bg-gray-600/5'
    };
    return statusMap[status];
};

// Get status prefix symbol
const getStatusPrefix = (status: LogStatus): string => {
    const prefixMap = {
        error: '[ERROR]',
        warn: '[WARN]',
        success: '[SUCCESS]',
        debug: '[DEBUG]',
        default: '[INFO]'
    };
    return prefixMap[status];
};

// Individual log item component
const LogItem: React.FC<LogItemProps> = ({ log, theme = 'dark' }) => {
    const statusStyle = getStatusStyle(log.status, theme);
    const statusPrefix = getStatusPrefix(log.status);
    const formattedTime = formatTime(log.time);

    const hoverStyle = theme === 'light' ? 'hover:bg-gray-100/50' : 'hover:bg-gray-800/30';
    const timeStyle = theme === 'light' ? 'text-gray-500' : 'text-gray-500';
    const messageStyle = theme === 'light' ? 'text-gray-700' : 'text-gray-300';
    const titleStyle = theme === 'light' ? 'text-gray-800' : 'text-gray-200';

    return (
        <div className={`border-l-2 px-2 py-2 font-mono text-xs transition-colors ${hoverStyle} ${statusStyle}`}>
            <div className="leading-relaxed">
                <span className={timeStyle}>{formattedTime}</span>
                <span className={timeStyle}>: </span>
                <span className="font-semibold">{statusPrefix}</span>
                <span className={titleStyle}>:</span>
                <span className={`font-semibold ${titleStyle}`}> {log.title}</span>
                <span className={messageStyle}>{" "}{log.message}</span>
            </div>
        </div>
    );
};

// Main log viewer container component
export const LogViewer: React.FC<LogViewerProps> = ({
                                                 logs,
                                                 height = '100%',
                                                 className = '',
                                                 theme = 'dark',
    lastVisited
                                             }) => {
    // Sort logs by time ascending
    const sortedLogs = [...logs].sort((a, b) => a.time - b.time);

    // Theme-based styles
    const contentStyle = theme === 'light'
        ? 'bg-white border border-gray-200'
        : 'bg-gray-900 border border-gray-700';

    const emptyTextStyle = theme === 'light'
        ? 'text-gray-500'
        : 'text-gray-500';

    const logContainerRef = useRef<HTMLDivElement>(null);

    // Function to scroll to bottom with multiple fallback methods
    const scrollToBottom = () => {
        if (logContainerRef.current) {
            const element = logContainerRef.current;
            if (element) {
                element.scrollTop = element.scrollHeight;
            }
        }
    };

    // Scroll to bottom on initial load and when logs change
    useEffect(() => {
        scrollToBottom();
        const last = _.last(logs)
        if (last) lastVisited?.(last)
    }, [logs, lastVisited]);

    return (
        <div
            ref={logContainerRef}
            className={`overflow-y-auto p-2 ${contentStyle} ${className}`}
            style={{ height, maxHeight: height }}
        >
            {sortedLogs.length === 0 ? (
                <div className={`font-mono text-sm italic ${emptyTextStyle}`}>
                    No log entries to display...
                </div>
            ) : (
                <div className="space-y-1">
                    {sortedLogs.map((log, index) => (
                        <LogItem
                            key={`${log.time}-${index}`} //  TODO
                            log={log}
                            theme={theme}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

export const SampleLogs: LogEntry[] = [
    {
        id: '1',
        title: 'Application Started',
        message: 'Server successfully initialized on port 3000',
        time: Math.floor(Date.now() / 1000) - 300,
        status: 'success'
    },
    {
        id: '2',
        title: 'Database Connection',
        message: 'Failed to connect to database after 3 attempts. Retrying in 5 seconds...',
        time: Math.floor(Date.now() / 1000) - 240,
        status: 'error'
    },
    {
        id: '3',
        title: 'User Authentication',
        message: 'Invalid token provided for user ID: 12345',
        time: Math.floor(Date.now() / 1000) - 180,
        status: 'warn'
    },
    {
        id: '4',
        title: 'Cache Update',
        message: 'Redis cache cleared and repopulated with 1,250 entries',
        time: Math.floor(Date.now() / 1000) - 120,
        status: 'default'
    },
    {
        id: '5',
        title: 'Debug Info',
        message: 'Memory usage: 45.2MB, CPU: 12%, Active connections: 23',
        time: Math.floor(Date.now() / 1000) - 60,
        status: 'debug'
    },
    {
        id: '6',
        title: 'API Request',
        message: 'GET /api/users/profile - 200 OK (124ms)',
        time: Math.floor(Date.now() / 1000) - 30,
        status: 'success'
    }
];


// Demo component
export default function LogViewerDemo() {

    const [logs, setLogs] = useState<LogEntry[]>(SampleLogs);
    const [theme, setTheme] = useState<'dark' | 'light'>('dark');

    // Add new log entry
    const addLog = () => {
        const statuses: LogStatus[] = ['error', 'warn', 'success', 'default', 'debug'];
        const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

        const newLog: LogEntry = {
            id: Date.now().toString(),
            title: 'New Entry',
            message: `Random log entry added at ${new Date().toLocaleTimeString()}`,
            time: Math.floor(Date.now() / 1000),
            status: randomStatus
        };

        setLogs(prev => [...prev, newLog]);
    };

    const clearLogs = () => {
        setLogs([]);
    };

    return (
        <div className={`p-6 min-h-screen ${theme === 'dark' ? 'bg-slate-800' : 'bg-slate-100'}`}>
            <div className="max-w-4xl mx-auto">
                <div className="mb-4 flex gap-4 items-center">
                    <h1 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        Log Viewer Demo
                    </h1>
                    <button
                        onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                        className={`px-3 py-1 rounded text-sm ${
                            theme === 'dark'
                                ? 'bg-slate-700 text-white hover:bg-slate-600'
                                : 'bg-slate-200 text-gray-900 hover:bg-slate-300'
                        }`}
                    >
                        Toggle Theme
                    </button>
                    <button
                        onClick={addLog}
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
                    >
                        Add Log Entry
                    </button>
                    <button
                        onClick={clearLogs}
                        className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
                    >
                        Clear Logs
                    </button>
                </div>

                <LogViewer
                    logs={logs}
                    height="500px"
                    theme={theme}
                    className="rounded-lg"
                />

                <div className={`mt-4 text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                    Total logs: {logs.length} | Click "Add Log Entry" to test auto-scroll
                </div>
            </div>
        </div>
    );
}