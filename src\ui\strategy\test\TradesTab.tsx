import {useMemo, useState} from "react";
import {Button, Splitter, Table, TableProps} from "antd";
import _ from "lodash";
import {BaseChart} from "../../../chart/BaseChart.tsx";
import {BacktestApiResponse} from "../../../data/BacktestOld.ts";
import {transformChartElementsByType} from "../../../utils/Transformer.ts";
import {ChartElements, emptyChartElements} from "../../../utils/ChartElements.ts";
import {Range, Time, UTCTimestamp} from "lightweight-charts";

// TODO: Selection mode: Single, Multiple
// TODO: Optimize Range offsets calculation when number of candles are high
// TODO: Reset Chart button

type TradesTabProps = {
    backtestApiResponse?: BacktestApiResponse
}

export function TradesTab({backtestApiResponse}: TradesTabProps) {
    const [chartWidth, setChartWidth] = useState(0);

    const filteredLines = useMemo(() => {
        return _.pickBy(backtestApiResponse?.chartElements.line ?? {}, (dt) => {
            return dt.group !== "stats"
            // return true
        })
    }, [backtestApiResponse?.chartElements.line]);

    const [selectedIds, setSelectedIds] = useState<string[]>([])
    const selectedRange: Range<Time> | undefined = useMemo(() => {
        const time = selectedIds.map((id) => {
            return backtestApiResponse?.closedPositions.find(x => x.actionInstanceId === id)?.positions.map(y => [y.tradeTime]).flat()
        }).filter(z => z !== undefined).flat()
        if (time.length === 0) return undefined
        const max = _.max(time);
        const min = _.min(time);
        if (max !== undefined && min !== undefined) {
            const offset = 2 * (max - min)
            return {
                from: min - offset as UTCTimestamp,
                to: max + offset as UTCTimestamp
            }
        }
        else return undefined
    }, [selectedIds, backtestApiResponse]);

    const positions = useMemo(() => {
        return (backtestApiResponse?.closedPositions ?? []).map((d, i) => ({
            ...d,
            selected: selectedIds.includes(d.actionInstanceId),
            index: i
        }))
    }, [backtestApiResponse, selectedIds]);

    const chartElements: ChartElements = useMemo(() => {
        if (!backtestApiResponse?.chartElements) return emptyChartElements()
        const tr = transformChartElementsByType(backtestApiResponse?.chartElements, {
            which: ["Buy", "Sell"],
            elementType: "marker",
            transformData(obj): ChartElements["marker"][string] {
                return {
                    ...obj,
                    data: obj.data.map((d) => {
                        if (!d.id || selectedIds.length === 0) return d
                        return {
                            ...d,
                            color: selectedIds.includes(d.id) ? d.color : (d.color + "00")
                        }
                    })
                };
            }
        })
        return tr
    }, [backtestApiResponse?.chartElements, selectedIds]);

    return (
        <div
            style={{width: "100%", height: "100%"}}
            className="h-full flex-1"
        >
            {/*{backtestApiResponse === undefined ? <Spin/> : (*/}
            <Splitter
                style={{height: "100%"}}
                className={""}
                onResize={(sizes) => setChartWidth(sizes[0])}
            >
                <Splitter.Panel defaultSize={"60%"} min={"30%"} max={"80%"}>
                    <BaseChart
                        visibleRange={selectedRange}
                        key={"adfasdf"}
                        mainChartData={chartElements.ohlc}
                        markers={chartElements.marker}
                        height={undefined}
                        width={chartWidth}
                        lines={[]}
                        lines={filteredLines}
                    />
                </Splitter.Panel>
                <Splitter.Panel>
                    <div style={{height: "100%"}} className="">
                        <Button onClick={() => setSelectedIds([])}>
                            Deselect
                        </Button>

                        <Table
                            rowKey={"actionInstanceId"}
                            rowClassName={(r) => {
                                // @ts-expect-error TODO
                                if (r.selected)
                                    return "bg-[#ACCAEE88]"
                                else return ""
                            }}
                            expandable={{
                                expandedRowKeys: selectedIds,
                                onExpand: (expanded, record) => {
                                    if (expanded)
                                        setSelectedIds([...selectedIds, record.actionInstanceId])
                                    else
                                        setSelectedIds(selectedIds.filter(d => d !== record.actionInstanceId))
                                },
                                expandedRowRender: (record) =>
                                    <ul style={{margin: 0}}>
                                        {
                                            record.positions.map(p =>
                                                <li>
                                                    {`${p.token} ${p.price} ${p.qty}`}
                                                </li>
                                            )
                                        }
                                    </ul>,
                                expandRowByClick: true,
                                showExpandColumn: false
                            }}
                            columns={columns}
                            size={"small"}
                            pagination={{
                                pageSize: 12,
                            }}
                            dataSource={positions}/>;
                    </div>
                </Splitter.Panel>
            </Splitter>
            {/*)}*/}
        </div>
    );
}

const columns: TableProps<BacktestApiResponse["closedPositions"][number] & {index: number}>['columns'] = [
    {
        title: 'Name',
        dataIndex: 'actionInstanceId',
        key: 'actionInstanceId',
        sorter: (a, b) => a.index - b.index,
        render: (text) => <a>{text}</a>,
    },
    {
        title: 'Pnl',
        dataIndex: 'cumulativePnl',
        key: 'cumulativePnl',
        align: "right",
        sorter: (a, b) => a.cumulativePnl - b.cumulativePnl,
        render: (text, record) => <span
            className={`font-bold ${record.cumulativePnl > 0 ? "text-[#59A14F]" : "text-[#E15759]"}`}>{text}</span>,
    },
    {
        title: 'Time Taken',
        dataIndex: 'timeTaken',
        key: 'timeTaken',
        sorter: (a, b) => a.timeTaken - b.timeTaken,
        render: (_, record) => (
            <>
                {formatTime(record.timeTaken)}
            </>
        )
    },
];

function formatTime(seconds: number): string {
    if (seconds < 60) return `${seconds} sec${seconds !== 1 ? "s" : ""}`;

    const minutes = seconds / 60;
    if (minutes < 60) return `${parseFloat(minutes.toFixed(1))} minute${minutes !== 1 ? "s" : ""}`;

    const hours = minutes / 60;
    if (hours < 24) return `${parseFloat(hours.toFixed(1))} hour${hours !== 1 ? "s" : ""}`;

    const days = hours / 24;
    return `${parseFloat(days.toFixed(1))} day${days !== 1 ? "s" : ""}`;
}