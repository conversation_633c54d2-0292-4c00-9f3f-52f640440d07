import {Block, CompositeValueType, Value} from "./Block.ts";
import {DbBlock} from "../types/study/db/DbBlock.ts";
import { StudyType } from "../types/study/StudyType.ts";
import {BlockUtils} from "./Utils.ts";
import {ConstantValue, SeriesValue, StudyValue} from "./SingleValue.ts";
import {ArithmeticOperator, BooleanOperator, ComparisonOperator} from "./Operator.ts";

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface CompositeValue<T extends CompositeValueType> extends Value<T> {}

export class Expression implements CompositeValue<"Expression">{
    type = "Expression" as const;
    value: Block[];

    constructor(value: Block[]) {
        this.value = value;
    }

    getValueType(): StudyType | undefined {
        const result = BlockUtils.calculateOutType(this.value);
        if (result.isSuccess) return result.value;
        else return undefined;
    }

    detailedString(): string {
        return "TODO";
    }

    getString(): string {
        return `( ${this.value.map(v => v.getString()).join(" ")} )`;
    }

    toDb(): DbBlock<"Expression"> {
        return {
            type: "Expression",
            value: this.value.map(b => b.toDb()),
        };
    }

    static fromDb(dbBlock: DbBlock<"Expression">): Expression {
        return new Expression(dbBlock.value.map(b => {
            switch (b.type) {
                case "Constant":
                    return ConstantValue.fromDb(b)
                case "Series":
                    return SeriesValue.fromDb(b)
                case "Study":
                    return StudyValue.fromDb(b)
                case "Expression":
                    return Expression.fromDb(b);
                case "BooleanOperator":
                    return BooleanOperator.fromDb(b)
                case "ComparisonOperator":
                    return ComparisonOperator.fromDb(b)
                case "ArithmeticOperator":
                    return ArithmeticOperator.fromDb(b)
            }
            throw new Error("Block type not defined.")
        }));
    }
}