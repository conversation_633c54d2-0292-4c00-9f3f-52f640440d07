import { StudyType } from "../types/study/StudyType"
import {Block, Operator} from "./Block"

function toPostfix(blocks: Block[]): Block[] {
    const postfix: Block[] = []
    let operators: Operator[] = []
    blocks.forEach(b => {
        if (Block.isValue(b)) postfix.push(b)
        else if (Block.isOperator(b)) {
            let top = operators[0]
            while(top != undefined && Operator.GetAssociativityPopCondition(b.associativity)(top, b)){
                postfix.push(operators.shift()!)
                top = operators[0]
            }
            operators = [b, ...operators]
        }
    })
    return postfix.concat(operators)
}

function calculateOutType(blocks: Block[]): { isSuccess: true, value: StudyType } | { isSuccess: false, error: string } {
    const postfix = toPostfix(blocks)
    let stack: StudyType[] = []
    for (let i = 0; i < postfix.length; i++) {
        const b = postfix[i];
        if (Block.isValue(b)) {
            const out = b.getValueType();
            if (!out) {
                return { isSuccess: false, error: `Value block has no type: ${JSON.stringify(b)}` }
            }
            stack = [out, ...stack]
        }
        else if (Block.isOperator(b)) {
            const t1 = stack.shift()
            const t2 = stack.shift()
            if (!t1 || !t2) {
                return { isSuccess: false, error: `Insufficient operands for operator: ${JSON.stringify(b)}` }
            }
            const res = b.getValueType(t1, t2);
            if (!res) {
                return { isSuccess: false, error: `Cannot determine output type for operator: ${JSON.stringify(b)} with types ${JSON.stringify(t1)} and ${JSON.stringify(t2)}` }
            }
            stack.push(res)
        } else {
            return { isSuccess: false, error: `Unknown block type: ${JSON.stringify(b)}` }
        }
    }

    if (stack.length !== 1) {
        return { isSuccess: false, error: `Malformed expression: stack has ${stack.length} elements` }
    }
    return { isSuccess: true, value: stack[0] };
}

export const BlockUtils = {
    calculateOutType: calculateOutType,
}