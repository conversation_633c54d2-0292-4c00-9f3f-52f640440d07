import {TimeSeriesStore} from "../../utils/Store.ts";
import {ChartElementDataItem} from "../../utils/ChartElementsParser.ts";
import {useEffect, useMemo} from "react";
import {OhlcData} from "lightweight-charts";
import {URL_PATH_DATA_OHLC} from "../../constants.ts";

export function useOhlcData(timeSeries: { token: string, timeframe: string } | undefined): undefined | {
    store: TimeSeriesStore<ChartElementDataItem["ohlc"]>;
    timeSeries: { token: string; timeframe: string }
} {
    const storeWithTimeSeries = useMemo(() => {
        if (timeSeries === undefined) return undefined;
        return {
            store: new TimeSeriesStore<ChartElementDataItem["ohlc"]>(i => i.time as number),
            timeSeries: timeSeries

        }
    }, [timeSeries]);

    useEffect(() => {
        if (storeWithTimeSeries !== undefined){
            getCandles(storeWithTimeSeries.timeSeries).then(candles => {
                console.log("setting data");
                storeWithTimeSeries.store.setData(candles)
            });
        }
    }, [storeWithTimeSeries]);

    return storeWithTimeSeries;
}

export async function getCandles(timeSeries: { token: string, timeframe: string }): Promise<OhlcData[]> {
    const response = await fetch(URL_PATH_DATA_OHLC, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(timeSeries),
    });
    if (!response.ok) {
        console.log("error fetching candles");
        return []; // TODO
    }

    return response.json();
}