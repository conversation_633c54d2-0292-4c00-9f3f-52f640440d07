import {BlockTypeMap, Operator} from "./Block.ts";
import {DbBlock} from "../types/study/db/DbBlock.ts";
import {StudyType} from "../types/study/StudyType.ts";

export type OperatorProps = {
    altName: string,
    precedence: number,
    associativity: "left" | "right"
}

export type ComparisonOperation = BlockTypeMap["ComparisonOperator"]
const ComparisonOperationProps: Record<ComparisonOperation, OperatorProps> = {
    IsGreaterThan: {
        altName: ">",
        precedence: 4,
        associativity: "left"
    },
    IsLessThan: {
        altName: "<",
        precedence: 4,
        associativity: "left"
    },
    IsGreaterThanOrEqualTo: {
        altName: ">=",
        precedence:  4,
        associativity: "left"
    },
    IsLessThanOrEqualTo: {
        altName: "<=",
        precedence:  4,
        associativity: "left"
    },
    IsEqualTo: {
        altName: "equals to",
        precedence: 3,
        associativity: "left"
    },
    IsNotEqualTo: {
        altName: "not equals to",
        precedence: 3,
        associativity: "left"
    },
} as const;

export class ComparisonOperator implements Operator<"ComparisonOperator"> {
    associativity: "left" | "right";
    precedence: number;
    type = "ComparisonOperator" as const;
    value: ComparisonOperation;

    constructor(value: ComparisonOperation) {
        this.value = value;
        this.associativity = ComparisonOperationProps[value].associativity;
        this.precedence = ComparisonOperationProps[value].precedence;
    }

    validInTypes: { typesForValue1: StudyType[]; typesForValue2: StudyType[] } = {
        typesForValue1: ["Number", "NullableNumber"],
        typesForValue2: ["Number", "NullableNumber"]
    };

    getValueType(typeOfValue1: StudyType, typeOfValue2: StudyType): StudyType | undefined {
        if (this.validInTypes.typesForValue1.includes(typeOfValue1) && this.validInTypes.typesForValue2.includes(typeOfValue2))
            return "Boolean";
        else
            return undefined;
    }

    detailedString(): string {
        return "TODO";
    }

    getString(): string {
        return ComparisonOperationProps[this.value].altName;
    }

    toDb(): DbBlock<"ComparisonOperator"> {
        return {
            type: "ComparisonOperator",
            value: this.value
        }
    }

    static fromDb(dbBlock: DbBlock<"ComparisonOperator">): ComparisonOperator {
        return new ComparisonOperator(dbBlock.value);
    }
}


export type ArithmeticOperation = BlockTypeMap["ArithmeticOperator"]
const ArithmeticOperationProps: Record<ArithmeticOperation, OperatorProps> = {
    Divide: {
        altName: "/",
        precedence: 6,
        associativity: "left"
    },
    Exponent: {
        altName: "^",
        precedence: 7,
        associativity: "right"
    },
    Minus: {
        altName: "-",
        precedence: 5,
        associativity: "left"
    },
    Multiply: {
        altName: "*",
        precedence: 6,
        associativity: "left"
    },
    Plus: {
        altName: "+",
        precedence: 5,
        associativity: "left"
    }
} as const;

export class ArithmeticOperator implements Operator<"ArithmeticOperator"> {
    associativity: "left" | "right";
    precedence: number;
    type = "ArithmeticOperator" as const;
    value: ArithmeticOperation;

    constructor(value: ArithmeticOperation) {
        this.value = value;
        this.associativity = ArithmeticOperationProps[value].associativity;
        this.precedence = ArithmeticOperationProps[value].precedence;
    }

    validInTypes: { typesForValue1: StudyType[]; typesForValue2: StudyType[] } = {
        typesForValue1: ["Number", "NullableNumber"],
        typesForValue2: ["Number", "NullableNumber"]
    };

    getValueType(typeOfValue1: StudyType, typeOfValue2: StudyType): StudyType | undefined {
        if (this.validInTypes.typesForValue1.includes(typeOfValue1) && this.validInTypes.typesForValue2.includes(typeOfValue2))
            return "Number";
        else
            return undefined;
    }


    detailedString(): string {
        return "TODO";
    }

    getString(): string {
        return ArithmeticOperationProps[this.value].altName;
    }

    toDb(): DbBlock<"ArithmeticOperator"> {
        return {
            type: "ArithmeticOperator",
            value: this.value
        }
    }
    static fromDb(dbBlock: DbBlock<"ArithmeticOperator">): ArithmeticOperator {
        return new ArithmeticOperator(dbBlock.value);
    }
}


export type BooleanOperation = BlockTypeMap["BooleanOperator"]
const BooleanOperationProps: Record<BooleanOperation, OperatorProps> = {
    And: {
        altName: "and",
        precedence: 2,
        associativity: "left"
    },
    Or: {
        altName: "or",
        precedence: 1,
        associativity: "left"
    }
} as const;

export class BooleanOperator implements Operator<"BooleanOperator"> {
    associativity: "left" | "right";
    precedence: number;
    type = "BooleanOperator" as const;
    value: BooleanOperation;

    constructor(value: BooleanOperation) {
        this.value = value;
        this.associativity = BooleanOperationProps[value].associativity;
        this.precedence = BooleanOperationProps[value].precedence;
    }

    validInTypes: { typesForValue1: StudyType[]; typesForValue2: StudyType[] } = {
        typesForValue1: ["Boolean", "NullableBoolean"],
        typesForValue2: ["Boolean", "NullableBoolean"]
    };

    getValueType(typeOfValue1: StudyType, typeOfValue2: StudyType): StudyType | undefined {
        if (this.validInTypes.typesForValue1.includes(typeOfValue1) && this.validInTypes.typesForValue2.includes(typeOfValue2))
            return "Boolean";
        else
            return undefined;
    }


    detailedString(): string {
        return "TODO";
    }

    getString(): string {
        return BooleanOperationProps[this.value].altName;
    }

    toDb(): DbBlock<"BooleanOperator"> {
        return {
            type: "BooleanOperator",
            value: this.value
        }
    }
    static fromDb(dbBlock: DbBlock<"BooleanOperator">): BooleanOperator {
        return new BooleanOperator(dbBlock.value);
    }
}
