import {DataStore, MergedStore} from "../../utils/Store.ts";
import {OhlcData} from "lightweight-charts";
import {ChartElementType} from "../../utils/ChartElements.ts";
import {ChartElementDataItem} from "../../utils/ChartElementsParser.ts";
import {TimeIntervalCalculator} from "../../time/TimeInterval.ts";

// TODO: For now for the elements whose interval is not found in the ohlc store, it is ignored, but maybe set it to the interval it is calculated to.
export function MorphedTimeSeriesStore<T extends ChartElementType>(
    intervalSeconds: number,
    ohlcStore: DataStore<OhlcData>,
    targetStore: DataStore<ChartElementDataItem[T]>
): MergedStore<OhlcData, ChartElementDataItem[T], ChartElementDataItem[T]> {
    const calculator = new TimeIntervalCalculator(intervalSeconds);
    // @ts-expect-error Type error. Resolve later
    return new MergedStore(
        ohlcStore,
        targetStore,
        (a, b) => {
            if (a === undefined || b === undefined) return undefined; // TODO
            const first = ohlcStore.first()?.time;
            if (first == undefined) return undefined;
            const start = calculator.calculateInterval(first as number, a.time as number).start;
            return {
                ...b,
                time: start
            }
        },
        (a) => {
            const first = ohlcStore.first()?.time;
            if (first == undefined) /*throw new Error("No first reference found.");*/ return 0
            return calculator.calculateInterval(first as number, a.time as number).start
        },
        (b) => {
            const first = ohlcStore.first()?.time;
            if (first == undefined) /*throw new Error("No first reference found.");*/ return 0
            return calculator.calculateInterval(first as number, b.time as number).start;
        }
    );
}