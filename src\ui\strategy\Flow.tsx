import { ReactFlow } from '@xyflow/react';

import '@xyflow/react/dist/style.css';

const initialNodes = [
    { id: '1', position: { x: 0, y: 0 }, data: { label: '1' } },
    { id: '2', position: { x: 100, y: 100 }, data: { label: '2' } },
    { id: '3', position: { x: 500, y: 200 }, data: { label: '3adf' } },
];
const initialEdges = [{ id: 'e1-2', source: '1', target: '2', label: "onexit" }];

export function Flow() {
    return (
        <div style={{ width: "100%", height: '100%' }} className={"bg-zinc-50"}>
            <ReactFlow
                nodes={initialNodes}
                edges={initialEdges}
                panOnDrag={false}
                zoomOnScroll={false}
                zoomOnPinch={false}
                zoomOnDoubleClick={false}
                fitView
            />
        </div>
    );
}