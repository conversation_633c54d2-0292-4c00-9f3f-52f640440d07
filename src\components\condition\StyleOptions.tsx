import { ChangeEvent, useState } from 'react';
import { BlockPicker } from 'react-color';
import * as Slider from '@radix-ui/react-slider';
import * as Dialog from '@radix-ui/react-dialog';

type SeriesMarkerShape = 'circle' | 'square' | 'arrowUp' | 'arrowDown';
type SeriesMarkerPosition = 'aboveBar' | 'belowBar' | 'inBar';

type LineStyle = {
    type: 'Line';
    lineColor: string;
};

type MarkerStyle = {
    type: 'Marker';
    color: string;
    shape: SeriesMarkerShape;
    position: SeriesMarkerPosition;
};

type HighlightStyle = {
    type: 'Highlight';
    highlightColor: string;
    highlightOpacity: number;
};

// Line Style Options Component
function LineStyleOptions({ style, onChange }: {
    style: LineStyle;
    onChange: (style: LineStyle) => void;
}) {
    const [showPicker, setShowPicker] = useState(false);

    return (
        <div className="space-y-3">
            <div className="space-y-2">
                <label className="block text-xs font-medium text-gray-600 uppercase tracking-wide">
                    Line Color
                </label>

                {/* Custom Color */}
                <div className="flex items-center gap-2">
                    <div
                        className="w-8 h-8 border-2 border-gray-300 rounded-lg cursor-pointer hover:border-blue-400 transition-colors flex items-center justify-center"
                        style={{ backgroundColor: style.lineColor }}
                        onClick={() => setShowPicker(!showPicker)}
                        aria-label="Select custom color"
                    >
                        <svg className="w-4 h-4 text-white drop-shadow-sm" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <span className="text-xs text-gray-500 font-mono">{style.lineColor}</span>
                </div>

                {showPicker && (
                    <div className="relative">
                        <div className="absolute top-2 left-0 z-20 bg-white rounded-lg shadow-lg border p-2">
                            <div className="flex justify-between items-center mb-2">
                                <span className="text-xs font-medium text-gray-700">Custom Color</span>
                                <button
                                    type="button"
                                    onClick={() => setShowPicker(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                            <BlockPicker
                                color={style.lineColor}
                                onChangeComplete={(color) => {
                                    onChange({ ...style, lineColor: color.hex });
                                    setShowPicker(false);
                                }}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

// Marker Style Options Component
function MarkerStyleOptions({ style, onChange }: {
    style: MarkerStyle;
    onChange: (style: MarkerStyle) => void;
}) {
    const [showPicker, setShowPicker] = useState(false);

    const shapeOptions = [
        { value: 'circle', label: 'Circle', icon: '●' },
        { value: 'square', label: 'Square', icon: '■' },
        { value: 'arrowUp', label: 'Arrow Up', icon: '▲' },
        { value: 'arrowDown', label: 'Arrow Down', icon: '▼' }
    ];

    const positionOptions = [
        { value: 'aboveBar', label: 'Above Bar' },
        { value: 'belowBar', label: 'Below Bar' },
        { value: 'inBar', label: 'In Bar' }
    ];

    return (
        <div className="space-y-4">
            {/* Color Section */}
            <div className="space-y-2">
                <label className="block text-xs font-medium text-gray-600 uppercase tracking-wide">
                    Marker Color
                </label>

                {/* Custom Color */}
                <div className="flex items-center gap-2">
                    <div
                        className="w-8 h-8 border-2 border-gray-300 rounded-lg cursor-pointer hover:border-blue-400 transition-colors flex items-center justify-center"
                        style={{ backgroundColor: style.color }}
                        onClick={() => setShowPicker(!showPicker)}
                        aria-label="Select custom color"
                    >
                        <svg className="w-4 h-4 text-white drop-shadow-sm" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <span className="text-xs text-gray-500 font-mono">{style.color}</span>
                </div>

                {showPicker && (
                    <div className="relative">
                        <div className="absolute top-2 left-0 z-20 bg-white rounded-lg shadow-lg border p-2">
                            <div className="flex justify-between items-center mb-2">
                                <span className="text-xs font-medium text-gray-700">Custom Color</span>
                                <button
                                    type="button"
                                    onClick={() => setShowPicker(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                            <BlockPicker
                                color={style.color}
                                onChangeComplete={(color) => {
                                    onChange({ ...style, color: color.hex });
                                    setShowPicker(false);
                                }}
                            />
                        </div>
                    </div>
                )}
            </div>

            {/* Shape Section */}
            <div className="space-y-2">
                <label className="block text-xs font-medium text-gray-600 uppercase tracking-wide">
                    Shape
                </label>
                <div className="grid grid-cols-2 gap-2">
                    {shapeOptions.map((option) => (
                        <button
                            key={option.value}
                            type="button"
                            className={`p-2 border rounded-lg text-sm font-medium transition-all hover:bg-gray-50 ${
                                style.shape === option.value
                                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                                    : 'border-gray-300 text-gray-700'
                            }`}
                            onClick={() => onChange({ ...style, shape: option.value as SeriesMarkerShape })}
                        >
                            <div className="flex items-center justify-center gap-1">
                                <span className="text-lg">{option.icon}</span>
                                <span className="text-xs">{option.label}</span>
                            </div>
                        </button>
                    ))}
                </div>
            </div>

            {/* Position Section */}
            <div className="space-y-2">
                <label className="block text-xs font-medium text-gray-600 uppercase tracking-wide">
                    Position
                </label>
                <div className="space-y-1">
                    {positionOptions.map((option) => (
                        <label key={option.value} className="flex items-center gap-2 cursor-pointer">
                            <input
                                type="radio"
                                name="position"
                                value={option.value}
                                checked={style.position === option.value}
                                onChange={(e) => onChange({ ...style, position: e.target.value as SeriesMarkerPosition })}
                                className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="text-sm text-gray-700">{option.label}</span>
                        </label>
                    ))}
                </div>
            </div>
        </div>
    );
}

// Highlight Style Options Component
function HighlightStyleOptions({ style, onChange }: {
    style: HighlightStyle;
    onChange: (style: HighlightStyle) => void;
}) {
    const [showPicker, setShowPicker] = useState(false);

    const presetOpacities = [0.1, 0.2, 0.3, 0.5, 0.7, 0.9];

    return (
        <div className="space-y-4">
            {/* Color Section */}
            <div className="space-y-2">
                <label className="block text-xs font-medium text-gray-600 uppercase tracking-wide">
                    Highlight Color
                </label>

                {/* Custom Color */}
                <div className="flex items-center gap-2">
                    <div
                        className="w-8 h-8 border-2 border-gray-300 rounded-lg cursor-pointer hover:border-blue-400 transition-colors flex items-center justify-center"
                        style={{ backgroundColor: style.highlightColor }}
                        onClick={() => setShowPicker(!showPicker)}
                        aria-label="Select custom color"
                    >
                        <svg className="w-4 h-4 text-white drop-shadow-sm" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <span className="text-xs text-gray-500 font-mono">{style.highlightColor}</span>
                </div>

                {showPicker && (
                    <div className="relative">
                        <div className="absolute top-2 left-0 z-20 bg-white rounded-lg shadow-lg border p-2">
                            <div className="flex justify-between items-center mb-2">
                                <span className="text-xs font-medium text-gray-700">Custom Color</span>
                                <button
                                    type="button"
                                    onClick={() => setShowPicker(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                            <BlockPicker
                                color={style.highlightColor}
                                onChangeComplete={(color) => {
                                    onChange({ ...style, highlightColor: color.hex });
                                    setShowPicker(false);
                                }}
                            />
                        </div>
                    </div>
                )}
            </div>

            {/* Opacity Section */}
            <div className="space-y-3">
                <label className="block text-xs font-medium text-gray-600 uppercase tracking-wide">
                    Opacity
                </label>

                {/* Preset Opacity Buttons */}
                <div className="flex flex-wrap gap-2 mb-3">
                    {presetOpacities.map((opacity) => (
                        <button
                            key={opacity}
                            type="button"
                            className={`px-3 py-1 text-xs rounded-full border transition-all hover:scale-105 ${
                                Math.abs(style.highlightOpacity - opacity) < 0.01
                                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                            }`}
                            onClick={() => onChange({ ...style, highlightOpacity: opacity })}
                        >
                            {Math.round(opacity * 100)}%
                        </button>
                    ))}
                </div>

                {/* Custom Opacity Slider */}
                <div className="space-y-2">
                    <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600">Custom</span>
                        <span className="text-xs text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">
                            {Math.round(style.highlightOpacity * 100)}%
                        </span>
                    </div>
                    <Slider.Root
                        className="relative flex items-center select-none touch-none w-full h-5"
                        value={[style.highlightOpacity]}
                        onValueChange={(value) => onChange({ ...style, highlightOpacity: value[0] })}
                        min={0}
                        max={1}
                        step={0.01}
                    >
                        <Slider.Track className="bg-gray-200 relative grow rounded-full h-2">
                            <Slider.Range className="absolute bg-blue-500 rounded-full h-full" />
                        </Slider.Track>
                        <Slider.Thumb className="block w-5 h-5 bg-white border-2 border-blue-500 rounded-full shadow-lg hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors" />
                    </Slider.Root>
                </div>

                {/* Opacity Preview */}
                <div className="mt-3 p-3 bg-gray-100 rounded-lg">
                    <div className="text-xs text-gray-600 mb-1">Preview</div>
                    <div className="relative h-8 bg-white rounded border overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-r from-gray-300 to-gray-400"></div>
                        <div
                            className="absolute inset-0 rounded"
                            style={{
                                backgroundColor: style.highlightColor,
                                opacity: style.highlightOpacity
                            }}
                        ></div>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Wrapped Dialog Components
export function LineStyleDialog({ isOpen, onOpenChange, style, onChange }: {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    style: LineStyle;
    onChange: (style: LineStyle) => void;
}) {
    return (
        <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
            <Dialog.Portal>
                <Dialog.Overlay className="fixed inset-0 bg-black bg-opacity-50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
                <Dialog.Content className="fixed left-[50%] top-[50%] z-50 w-full max-w-md translate-x-[-50%] translate-y-[-50%] bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-lg">
                    <Dialog.Title className="text-lg font-semibold text-gray-900 mb-4">
                        Line Style Options
                    </Dialog.Title>
                    <LineStyleOptions style={style} onChange={onChange} />
                    <Dialog.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </Dialog.Close>
                </Dialog.Content>
            </Dialog.Portal>
        </Dialog.Root>
    );
}

export function MarkerStyleDialog({ isOpen, onOpenChange, style, onChange }: {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    style: MarkerStyle;
    onChange: (style: MarkerStyle) => void;
}) {
    return (
        <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
            <Dialog.Portal>
                <Dialog.Overlay className="fixed inset-0 bg-black bg-opacity-50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
                <Dialog.Content className="fixed left-[50%] top-[50%] z-50 w-full max-w-md translate-x-[-50%] translate-y-[-50%] bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-lg">
                    <Dialog.Title className="text-lg font-semibold text-gray-900 mb-4">
                        Marker Style Options
                    </Dialog.Title>
                    <MarkerStyleOptions style={style} onChange={onChange} />
                    <Dialog.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </Dialog.Close>
                </Dialog.Content>
            </Dialog.Portal>
        </Dialog.Root>
    );
}

export function HighlightStyleDialog({ isOpen, onOpenChange, style, onChange }: {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    style: HighlightStyle;
    onChange: (style: HighlightStyle) => void;
}) {
    return (
        <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
            <Dialog.Portal>
                <Dialog.Overlay className="fixed inset-0 bg-black bg-opacity-50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
                <Dialog.Content className="fixed left-[50%] top-[50%] z-50 w-full max-w-md translate-x-[-50%] translate-y-[-50%] bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-lg">
                    <Dialog.Title className="text-lg font-semibold text-gray-900 mb-4">
                        Highlight Style Options
                    </Dialog.Title>
                    <HighlightStyleOptions style={style} onChange={onChange} />
                    <Dialog.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </Dialog.Close>
                </Dialog.Content>
            </Dialog.Portal>
        </Dialog.Root>
    );
}

// Usage Example Component
export default function StyleOptionsExample() {
    const [lineStyle, setLineStyle] = useState<LineStyle>({
        type: 'Line',
        lineColor: '#3B82F6'
    });

    const [markerStyle, setMarkerStyle] = useState<MarkerStyle>({
        type: 'Marker',
        color: '#EF4444',
        shape: 'circle',
        position: 'aboveBar'
    });

    const [highlightStyle, setHighlightStyle] = useState<HighlightStyle>({
        type: 'Highlight',
        highlightColor: '#10B981',
        highlightOpacity: 0.3
    });

    const [lineDialogOpen, setLineDialogOpen] = useState(false);
    const [markerDialogOpen, setMarkerDialogOpen] = useState(false);
    const [highlightDialogOpen, setHighlightDialogOpen] = useState(false);

    return (
        <div className="p-6 max-w-2xl mx-auto">
            <h1 className="text-2xl font-bold mb-6">Style Options Dialog Demo</h1>

            <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                        <div
                            className="w-4 h-4 rounded"
                            style={{ backgroundColor: lineStyle.lineColor }}
                        />
                        <div>
                            <div className="font-medium">Line Style</div>
                            <div className="text-sm text-gray-500">{lineStyle.lineColor}</div>
                        </div>
                    </div>
                    <button
                        onClick={() => setLineDialogOpen(true)}
                        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        Edit Line Style
                    </button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                        <div
                            className="w-4 h-4 rounded"
                            style={{ backgroundColor: markerStyle.color }}
                        />
                        <div>
                            <div className="font-medium">Marker Style</div>
                            <div className="text-sm text-gray-500">
                                {markerStyle.shape} - {markerStyle.position}
                            </div>
                        </div>
                    </div>
                    <button
                        onClick={() => setMarkerDialogOpen(true)}
                        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        Edit Marker Style
                    </button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                        <div
                            className="w-4 h-4 rounded"
                            style={{
                                backgroundColor: highlightStyle.highlightColor,
                                opacity: highlightStyle.highlightOpacity
                            }}
                        />
                        <div>
                            <div className="font-medium">Highlight Style</div>
                            <div className="text-sm text-gray-500">
                                {highlightStyle.highlightColor} - {Math.round(highlightStyle.highlightOpacity * 100)}%
                            </div>
                        </div>
                    </div>
                    <button
                        onClick={() => setHighlightDialogOpen(true)}
                        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        Edit Highlight Style
                    </button>
                </div>
            </div>

            <LineStyleDialog
                isOpen={lineDialogOpen}
                onOpenChange={setLineDialogOpen}
                style={lineStyle}
                onChange={setLineStyle}
            />

            <MarkerStyleDialog
                isOpen={markerDialogOpen}
                onOpenChange={setMarkerDialogOpen}
                style={markerStyle}
                onChange={setMarkerStyle}
            />

            <HighlightStyleDialog
                isOpen={highlightDialogOpen}
                onOpenChange={setHighlightDialogOpen}
                style={highlightStyle}
                onChange={setHighlightStyle}
            />
        </div>
    );
}