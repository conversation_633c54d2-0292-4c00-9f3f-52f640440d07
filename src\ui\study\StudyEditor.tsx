import {BaseEditor, Descendant} from "slate";
import {ReactEditor} from "slate-react";
import {HistoryEditor} from "slate-history";

export const obj ={}
// declare module 'slate' {
//     interface CustomTypes {
//         Editor: BaseEditor & ReactEditor & HistoryEditor
//         Element: ExpressionElement
//         Text: Text
//     }
// }
// export type Text = {
//     text: string
// }
//
// export type ValueElement = {
//     type: "value",
//     children: Text[]
// }
// export type OperatorElement = {
//     type: "operator",
//     children: Text[]
// }
//
// export type ExpressionElement = {
//     type: "expression",
//     isNested: boolean,
//     children: (ValueElement | OperatorElement | Text)[]
// }
// export const initialValue: Descendant[] = [
//     {
//         type: 'expression',
//         isNested: false,
//         children: [
//             {
//                 text: '',
//             }
//         ]
//     }
// ]