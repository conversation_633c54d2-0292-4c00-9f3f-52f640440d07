import {useEffect, useState} from "react";
import {TCData} from "../StrategyTerminal.ts";
import {runBacktest} from "./Strategy.ts";
import {useParams} from "react-router";

export function useBacktestData(): TCData {

    const [TCData, setTCData] = useState<TCData>({});

    const { symbol } = useParams<{ symbol: string }>();

    useEffect(() => {
        // runBacktest("NSE:NIFTY25JUN25000CE").then(dt => setTCData(dt));
        runBacktest(`NSE:${symbol}`).then(dt => setTCData(dt));
    }, [symbol]);

    return TCData;
}
