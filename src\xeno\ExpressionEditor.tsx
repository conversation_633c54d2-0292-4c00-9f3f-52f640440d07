import React, {
    useMemo,
    useCallback,
    useRef,
    useEffect,
    useState,
    Fragment,
    ReactNode,
    KeyboardEventHandler,
} from 'react'
import {Editor, Transforms, Range, createEditor, Descendant, BaseRange, BaseEditor, Node, NodeEntry, Path, Text} from 'slate'
import {HistoryEditor, withHistory} from 'slate-history'
import {
    Slate,
    Editable,
    ReactEditor,
    withReact,
    useSelected,
    useFocused,
    RenderLeafProps,
    RenderElementProps
} from 'slate-react'
import {createPortal} from "react-dom"
import {Block, BlockType} from "./Block"
import {SeriesValue, StudyValue, ConstantValue} from "./SingleValue.ts"
import {ArithmeticOperator, ComparisonOperator, BooleanOperator} from "./Operator.ts"
import {StudyTypeValue} from "../types/study/StudyType"
import {StudyParam} from "../types/study/ReadStudyParam"
import {Expression} from "./CompositeValue.ts"

const IS_MAC = typeof navigator !== 'undefined' && navigator.platform.includes('Mac')

export interface LText {
    text: string
}

export interface ValueElement {
    type: "value"
    character: string
    children: LText[]
    block: Block<BlockType>
}

export interface OperatorElement {
    type: "operator"
    character: string
    children: LText[]
    block: Block<BlockType>
}

export interface ExpressionElement {
    type: "expression"
    block: Block<BlockType>
    children: (ValueElement | OperatorElement | LText | ExpressionElement)[]
}

export type BlockElement = ExpressionElement | ValueElement | OperatorElement

export interface CustomElement {
    type: "custom"
    children: (ValueElement | OperatorElement | LText | ExpressionElement)[]
}

declare module 'slate' {
    interface CustomTypes {
        Editor: BaseEditor & ReactEditor & HistoryEditor
        Element: CustomElement | ExpressionElement | OperatorElement | ValueElement
        Text: LText
    }
}

const initialValue: Descendant[] = [
    {
        type: 'custom',
        children: [
            {
                text: '',
            }
        ]
    }
]

export const Portal: React.FC<{ children?: ReactNode }> = ({children}) => {
    return typeof document === 'object'
        ? createPortal(children, document.body)
        : null
}

// Enhanced Option interface with categories and descriptions
interface Option {
    text: string
    description?: string
    category: 'series' | 'operator' | 'study' | 'constant' | 'expression'
    getElement: () => BlockElement
}

// Type-safe series values
type SeriesType = "Open" | "High" | "Low" | "Close"
const SERIES_VALUES: SeriesType[] = ["Close", "Open", "High", "Low"]

const SeriesOptions: Option[] = SERIES_VALUES.map(str => ({
    text: str,
    description: `${str} price`,
    category: 'series' as const,
    getElement: (): BlockElement => ({
        type: "value",
        character: str,
        children: [{text: ""}],
        block: new SeriesValue(str)
    }),
}))

const SeriesOffsetOptions: Option[] = SERIES_VALUES.map(str => ({
    text: `${str}-1`,
    description: `Previous ${str} price`,
    category: 'series' as const,
    getElement: (): BlockElement => ({
        type: "value",
        character: `${str}-1`,
        children: [{text: ""}],
        block: new SeriesValue(str, 1)
    }),
}))

// Type-safe arithmetic operations
const ARITHMETIC_OPS = [
    { symbol: "+", operation: "Plus" as const, description: "Addition" },
    { symbol: "-", operation: "Minus" as const, description: "Subtraction" },
    { symbol: "*", operation: "Multiply" as const, description: "Multiplication" },
    { symbol: "/", operation: "Divide" as const, description: "Division" },
    { symbol: "^", operation: "Exponent" as const, description: "Exponentiation" }
]

const ArithmeticOptions: Option[] = ARITHMETIC_OPS.map(({symbol, operation, description}) => ({
    text: symbol,
    description,
    category: 'operator' as const,
    getElement: (): BlockElement => ({
        type: "operator",
        character: symbol,
        children: [{text: ""}],
        block: new ArithmeticOperator(operation)
    }),
}))

// Type-safe comparison operations
const COMPARISON_OPS = [
    { symbol: ">", operation: "IsGreaterThan" as const, description: "Greater than" },
    { symbol: "<", operation: "IsLessThan" as const, description: "Less than" },
    { symbol: ">=", operation: "IsGreaterThanOrEqualTo" as const, description: "Greater than or equal" },
    { symbol: "<=", operation: "IsLessThanOrEqualTo" as const, description: "Less than or equal" },
    { symbol: "==", operation: "IsEqualTo" as const, description: "Equal to" },
    { symbol: "!=", operation: "IsNotEqualTo" as const, description: "Not equal to" }
]

const ComparisonOptions: Option[] = COMPARISON_OPS.map(({symbol, operation, description}) => ({
    text: symbol,
    description,
    category: 'operator' as const,
    getElement: (): BlockElement => ({
        type: "operator",
        character: symbol,
        children: [{text: ""}],
        block: new ComparisonOperator(operation)
    }),
}))

// Type-safe boolean operations
const BOOLEAN_OPS = [
    { symbol: "and", operation: "And" as const, description: "Logical AND" },
    { symbol: "or", operation: "Or" as const, description: "Logical OR" }
]

const BooleanOptions: Option[] = BOOLEAN_OPS.map(({symbol, operation, description}) => ({
    text: symbol,
    description,
    category: 'operator' as const,
    getElement: (): BlockElement => ({
        type: "operator",
        character: symbol,
        children: [{text: ""}],
        block: new BooleanOperator(operation)
    }),
}))

// Mock study options with better typing
const STUDY_TYPES = [
    { name: "ema", description: "Exponential Moving Average" },
    { name: "sma", description: "Simple Moving Average" },
    { name: "rsi", description: "Relative Strength Index" },
    { name: "macd", description: "Moving Average Convergence Divergence" },
    { name: "bb", description: "Bollinger Bands" }
]

const StudyOptions: Option[] = STUDY_TYPES.map(({name, description}) => ({
    text: name,
    description,
    category: 'study' as const,
    getElement: (): BlockElement => ({
        type: "value",
        character: name,
        children: [{text: ""}],
        block: new StudyValue({paramName: name} as StudyParam)
    }),
}))

// Common constant values
const CONSTANT_VALUES = ["4", "10", "14", "20", "26", "50", "100", "200", "2000"]

const ConstantOptions: Option[] = CONSTANT_VALUES.map(str => ({
    text: str,
    description: `Constant value ${str}`,
    category: 'constant' as const,
    getElement: (): BlockElement => ({
        type: "value",
        character: str,
        children: [{text: ""}],
        block: new ConstantValue({value: parseFloat(str), type: "Number"})
    }),
}))

// Expression option for parentheses
const ExpressionOptions: Option[] = [
    {
        text: "()",
        description: "Parentheses for grouping expressions",
        category: 'expression' as const,
        getElement: (): BlockElement => ({
            type: "expression",
            block: new Expression([]),
            children: [{
                type: "custom",
                children: [{ text: "\u200B" }]
            }]
        }),
    }
]

const AllOptions: Option[] = [
    ...SeriesOptions,
    ...SeriesOffsetOptions,
    ...ArithmeticOptions,
    ...ComparisonOptions,
    ...BooleanOptions,
    ...StudyOptions,
    ...ConstantOptions,
    ...ExpressionOptions
]

const getAncestorAtSelection = (editor: Editor, level = 1): NodeEntry | null => {
    const {selection} = editor

    if (!selection) return null

    try {
        const [, path] = Node.first({
            children: editor.children
        }, selection.anchor.path)

        const ancestorPath = path.slice(0, -level)

        if (ancestorPath.length === 0) return null

        return Editor.node(editor, ancestorPath)
    } catch (error) {
        console.warn('Error getting ancestor at selection:', error)
        return null
    }
}

const getCurrentWordAtSelection = (editor: Editor): BaseRange | null => {
    const {selection} = editor

    if (!selection) return null

    const start = selection.anchor

    try {
        const node = Editor.node(editor, start.path) as NodeEntry<any>

        if (!node || typeof node[0].text !== 'string') return null

        const text = node[0].text
        const isAtEnd = start.offset === text.length || /\s/.test(text[start.offset])
        const hasWordBefore = start.offset > 0 && !/\s/.test(text[start.offset - 1])

        if (isAtEnd && !hasWordBefore) return null

        let wordStart = start.offset
        while (wordStart > 0 && !/\s/.test(text[wordStart - 1])) {
            wordStart--
        }

        let wordEnd = isAtEnd ? start.offset : start.offset
        while (wordEnd < text.length && !/\s/.test(text[wordEnd])) {
            wordEnd++
        }

        if (wordStart === wordEnd) return null

        return {
            anchor: {path: start.path, offset: wordStart},
            focus: {path: start.path, offset: wordEnd}
        }
    } catch (error) {
        console.warn('Error getting current word:', error)
        return null
    }
}

const extractBlocks = (descendants: Descendant[]): Block<BlockType>[] => {
    if (!descendants[0]) return []

    const extractFromChildren = (children: any[]): Block<BlockType>[] => {
        const blocks: Block<BlockType>[] = []

        for (const child of children) {
            if ('type' in child) {
                if (child.type === 'expression') {
                    // For expressions, extract nested blocks and create Expression block
                    const nestedBlocks = extractFromChildren(child.children)
                    const expressionBlock = new Expression(nestedBlocks)
                    blocks.push(expressionBlock)
                } else if (child.type === 'value' || child.type === 'operator') {
                    blocks.push(child.block)
                }
            }
        }

        return blocks
    }

    const children = descendants[0].children
    return extractFromChildren(children)
}

const getCategoryColor = (category: Option['category']): string => {
    switch (category) {
        case 'series': return 'text-purple-600'
        case 'operator': return 'text-orange-600'
        case 'study': return 'text-blue-600'
        case 'constant': return 'text-green-600'
        case 'expression': return 'text-indigo-600'
        default: return 'text-gray-600'
    }
}

interface ExpressionEditorProps {
    width?: string
    placeholder?: string
    onChange?: (blocks: Block<BlockType>[]) => void
    onError?: (error: string) => void
    disabled?: boolean
}

export const ExpressionEditor: React.FC<ExpressionEditorProps> = ({
                                                                      width = "100%",
                                                                      placeholder = "Start typing to build your expression...",
                                                                      onChange,
                                                                      onError,
                                                                      disabled = false
                                                                  }) => {
    const dropdownRef = useRef<HTMLDivElement | null>(null)
    const [target, setTarget] = useState<Range | null>(null)
    const [index, setIndex] = useState(0)
    const [search, setSearch] = useState('')

    const renderElement = useCallback((props: RenderElementProps) => <ElementComp {...props} />, [])
    const renderLeaf = useCallback((props: RenderLeafProps) => <Leaf {...props} />, [])

    const editor = useMemo(
        () => withMentions(withReact(withHistory(createEditor()))),
        []
    )

    const prevBlocks = useRef<Block<BlockType>[]>([])
    const _blocks = useRef<Block<BlockType>[]>([])

    const blocks = useMemo(() => {
        if (!editor.children) return []

        try {
            const newBlocks = extractBlocks(editor.children)
            prevBlocks.current = _blocks.current
            _blocks.current = newBlocks
            return newBlocks
        } catch (error) {
            onError?.('Error extracting blocks')
            console.error('Error extracting blocks:', error)
            return []
        }
    }, [editor.children, onError])

    useEffect(() => {
        try {
            const prevString = JSON.stringify(prevBlocks.current.map(b => b.getString()))
            const currentString = JSON.stringify(blocks.map(b => b.getString()))

            if (prevString !== currentString) {
                onChange?.(blocks)
            }
        } catch (error) {
            onError?.('Error processing blocks')
            console.error('Error processing blocks:', error)
        }
    }, [blocks, onChange, onError])

    const filteredOptions = useMemo(() => {
        if (search === "" || search === "\u200B") return []

        const searchLower = search.replace(/\u200B/g, '').trim().toLowerCase()
        return AllOptions
            .filter(option =>
                option.text.toLowerCase().startsWith(searchLower) ||
                option.description?.toLowerCase().includes(searchLower)
            )
            .slice(0, 8) // Limit to 8 options for better UX
    }, [search])

    const onKeyDown: KeyboardEventHandler<HTMLDivElement> = useCallback(
        event => {
            if (disabled) return

            if (target && filteredOptions.length > 0) {
                switch (event.key) {
                    case 'ArrowDown': {
                        event.preventDefault()
                        setIndex(prev => prev >= filteredOptions.length - 1 ? 0 : prev + 1)
                        break
                    }
                    case 'ArrowUp': {
                        event.preventDefault()
                        setIndex(prev => prev <= 0 ? filteredOptions.length - 1 : prev - 1)
                        break
                    }
                    case 'Tab':
                    case 'Enter': {
                        event.preventDefault()
                        if (filteredOptions[index]) {
                            Transforms.select(editor, target)
                            insertMention(editor, filteredOptions[index].getElement())
                            setTarget(null)
                            setSearch('')
                        }
                        break
                    }
                    case 'Escape': {
                        event.preventDefault()
                        setTarget(null)
                        setSearch('')
                        break
                    }
                }
            }

            switch (event.key) {
                case 'Enter': {
                    if (!target || filteredOptions.length === 0) {
                        event.preventDefault()
                    }
                    break
                }
                case 'Backspace': {
                    const ancestor = getAncestorAtSelection(editor);
                    if (ancestor) {
                        const [node, path] = ancestor;

                        if (node?.type !== "custom") {
                            const onlyChild = node.children.length === 1;
                            const onlyText = onlyChild && Text.isText(node.children[0]);

                            // Check if node contains only empty or zero-width space text
                            const hasOnlyEmptyText =
                                onlyText && node.children[0].text.replace(/\u200B/g, '').trim() === '';

                            // Ensure no blocks like value/operator/expression exist in children
                            const noBlocks =
                                !node.children.some(child =>
                                    typeof child === 'object' &&
                                    'type' in child &&
                                    (child.type === 'value' || child.type === 'operator' || child.type === 'expression')
                                );

                            if (hasOnlyEmptyText && noBlocks) {
                                Transforms.removeNodes(editor, { at: path, voids: true });
                                event.preventDefault();
                            }
                        }
                    }
                    break;
                }


                case 'ArrowRight': {
                    const { selection } = editor;
                    if (selection) {
                        const [node, path] = Editor.node(editor, selection);
                        if (node && 'text' in node && selection.anchor.offset === node.text.replace(/\u200B/g, '').length) {
                            const nextEntry = Editor.next(editor, { at: selection });
                            if (nextEntry) {
                                const [nextNode, nextPath] = nextEntry;
                                if (nextNode && 'type' in nextNode && nextNode.type === 'expression') {
                                    const insideExpressionPath = [...nextPath, 0];
                                    try {
                                        const [insideNode] = Editor.node(editor, insideExpressionPath);
                                        const offset = insideNode && 'text' in insideNode ? 0 : 0;

                                        Transforms.select(editor, {
                                            anchor: { path: insideExpressionPath, offset },
                                            focus: { path: insideExpressionPath, offset }
                                        });
                                        event.preventDefault();
                                        return;
                                    } catch (error) {
                                        console.warn('Error navigating into expression:', error);
                                    }
                                }
                            }
                        }
                    }

                    // Fallback
                    const ancestor = getAncestorAtSelection(editor);
                    if (ancestor) {
                        const [, path] = ancestor;
                        const after = Editor.after(editor, path, { unit: "character" });
                        const afterLine = Editor.after(editor, path);
                        if (!after && afterLine) {
                            Transforms.insertNodes(editor, { text: "" }, { at: afterLine });
                            Transforms.move(editor, { distance: 1, unit: "offset" });
                        }
                    }
                    break;
                }

                case 'ArrowLeft': {
                    const { selection } = editor;
                    if (selection) {
                        const [node, path] = Editor.node(editor, selection);
                        if (node && 'text' in node && selection.anchor.offset === 0) {
                            const prevEntry = Editor.previous(editor, { at: selection });
                            if (prevEntry) {
                                const [prevNode, prevPath] = prevEntry;
                                if (prevNode && 'type' in prevNode && prevNode.type === 'expression') {
                                    const insideExpressionPath = [...prevPath, 0];
                                    try {
                                        const [insideNode] = Editor.node(editor, insideExpressionPath);
                                        const textLength = insideNode && 'text' in insideNode
                                            ? insideNode.text.replace(/\u200B/g, '').length
                                            : 0;

                                        Transforms.select(editor, {
                                            anchor: { path: insideExpressionPath, offset: textLength },
                                            focus: { path: insideExpressionPath, offset: textLength }
                                        });
                                        event.preventDefault();
                                        return;
                                    } catch (error) {
                                        console.warn('Error navigating into expression:', error);
                                    }
                                }
                            }
                        }
                    }
                    break;
                }

            }
        },
        [filteredOptions, editor, index, target, disabled]
    )

    // Enhanced dropdown positioning
    useEffect(() => {
        if (target && filteredOptions.length > 0 && dropdownRef.current) {
            try {
                const domRange = ReactEditor.toDOMRange(editor, target)
                const rect = domRange.getBoundingClientRect()
                const dropdown = dropdownRef.current

                // Position dropdown below the cursor
                let top = rect.top + window.scrollY + 24
                let left = rect.left + window.scrollX

                // Ensure dropdown doesn't go off-screen
                const dropdownRect = dropdown.getBoundingClientRect()
                const viewportWidth = window.innerWidth
                const viewportHeight = window.innerHeight

                if (left + dropdownRect.width > viewportWidth) {
                    left = viewportWidth - dropdownRect.width - 10
                }

                if (top + dropdownRect.height > viewportHeight) {
                    top = rect.top + window.scrollY - dropdownRect.height - 5
                }

                dropdown.style.top = `${top}px`
                dropdown.style.left = `${left}px`
            } catch (error) {
                console.warn('Error positioning dropdown:', error)
            }
        }
    }, [filteredOptions.length, editor, index, search, target])

    const handleOptionSelect = useCallback((option: Option) => {
        if (target) {
            Transforms.select(editor, target)
            insertMention(editor, option.getElement())
            setTarget(null)
            setSearch('')
        }
    }, [editor, target])

    return (
        <div className="relative">
            <Slate
                editor={editor}
                initialValue={initialValue}
                onChange={() => {
                    if (disabled) return

                    const {selection} = editor
                    if (selection && Range.isCollapsed(selection)) {
                        const currentWord = getCurrentWordAtSelection(editor)
                        const newTarget = currentWord ?? selection
                        let newSearch = ""

                        if (currentWord) {
                            try {
                                newSearch = Editor.string(editor, currentWord)
                            } catch (error) {
                                console.warn('Error getting search string:', error)
                            }
                        }

                        setTarget(newTarget)
                        setSearch(newSearch)
                        setIndex(0)
                        return
                    }

                    setTarget(null)
                    setSearch('')
                }}
            >
                <div className="relative">
                    <Editable
                        style={{width}}
                        renderElement={renderElement}
                        renderLeaf={renderLeaf}
                        renderPlaceholder={({attributes, children}) => (
                            <span
                                {...attributes}
                                className="select-none text-gray-400 italic"
                            >
                                {children}
                            </span>
                        )}
                        onKeyDown={onKeyDown}
                        className={`
                            outline-none bg-white border-2 border-gray-200 rounded-lg shadow-sm p-3 
                            transition-all duration-200 min-h-[40px]
                            ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'focus:border-blue-400 focus:shadow-md'}
                        `}
                        placeholder={placeholder}
                        readOnly={disabled}
                    />

                    {/* Simplified dropdown */}
                    {target && filteredOptions.length > 0 && (
                        <Portal>
                            <div
                                ref={dropdownRef}
                                className="fixed z-[9999] pointer-events-auto bg-white border border-gray-200 rounded-lg shadow-lg py-1 max-h-64 overflow-y-auto min-w-[200px]"
                                style={{
                                    top: '-9999px',
                                    left: '-9999px',
                                }}
                            >
                                {filteredOptions.map((option, i) => (
                                    <div
                                        key={`${option.text}-${option.category}`}
                                        onClick={() => handleOptionSelect(option)}
                                        className={`
                                            px-3 py-2 cursor-pointer transition-colors duration-150 flex items-center justify-between
                                            ${i === index ? 'bg-blue-50 border-l-2 border-blue-400' : 'hover:bg-gray-50'}
                                        `}
                                    >
                                        <span className="font-medium text-md text-gray-900">
                                            {option.text}
                                        </span>
                                        <span className={`text-xs uppercase tracking-wide ${getCategoryColor(option.category)}`}>
                                            {option.category}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </Portal>
                    )}
                </div>
            </Slate>
        </div>
    )
}

const withMentions = (editor: Editor): Editor => {
    const {isInline, isVoid, markableVoid} = editor

    editor.isInline = element => {
        return element.type === 'expression' || element.type === "value" || element.type === "operator"
            ? true
            : isInline(element)
    }

    editor.isVoid = element => {
        return element.type === 'value' || element.type === "operator"
            ? true
            : isVoid(element)
    }

    editor.markableVoid = element => {
        return element.type === 'value' || element.type === "operator"
            ? true
            : markableVoid(element)
    }

    return editor
}

const insertMention = (editor: Editor, element: BlockElement): void => {
    console.log(element)
    Transforms.insertNodes(editor, [element])

    // If it's an expression element, move cursor inside it
    if (element.type === 'expression') {
        // Find the expression node that was just inserted
        const {selection} = editor
        if (selection) {
            try {
                // Get the path of the inserted expression
                const [, insertedPath] = Editor.previous(editor, {at: selection}) || [null, null]
                if (insertedPath) {
                    // Navigate to the first child of the expression (inside the parentheses)
                    const insideExpressionPath = [...insertedPath, 0]
                    setTimeout(() => {
                        Transforms.select(editor, {
                            anchor: {path: insideExpressionPath, offset: 0},
                            focus: {path: insideExpressionPath, offset: 0}
                        })
                    }, 10)
                    return
                }
            } catch (error) {
                console.warn('Error positioning cursor inside expression:', error)
            }
        }
    }

    console.log("moving")
    Transforms.move(editor)
}

const Leaf: React.FC<RenderLeafProps> = ({attributes, children}) => {
    return <span {...attributes}>{children}</span>
}

const ElementComp: React.FC<RenderElementProps> = (props) => {
    const {attributes, children, element} = props

    if (element.type === "expression") {
        return (
            <span {...attributes} className="inline-flex items-center">
                <span contentEditable={false} className="text-gray-600 font-bold text-lg">
                    (
                </span>
                <span className="mx-1 min-w-[1px]">{children}</span>
                <span contentEditable={false} className="text-gray-600 font-bold text-lg">
                    )
                </span>
            </span>
        )
    }

    switch (element.type) {
        case 'value':
        case 'operator':
            return <Mention {...props} />
        default:
            return <p {...attributes} className="min-h-[1.5rem]">{children}</p>
    }
}

const Mention: React.FC<RenderElementProps> = (props) => {
    const {attributes, children, element} = props
    const selected = useSelected()
    const focused = useFocused()

    if (element.type !== 'value' && element.type !== 'operator') {
        return <span {...attributes}>{children}</span>
    }

    const isSelected = selected && focused
    const blockType = element.block.type

    // Enhanced styling based on block type
    const getBlockStyles = () => {
        const baseStyles = "inline-flex items-center px-2 py-1 rounded-md text-sm font-medium transition-all duration-150 mx-0.5"

        switch (blockType) {
            case "Series":
                return `${baseStyles} bg-purple-100 text-purple-800 border border-purple-200`
            case "Study":
                return `${baseStyles} bg-blue-100 text-blue-800 border border-blue-200`
            case "Constant":
                return `${baseStyles} bg-green-100 text-green-800 border border-green-200`
            case "ArithmeticOperator":
                return `${baseStyles} bg-orange-100 text-orange-800 border border-orange-200`
            case "ComparisonOperator":
                return `${baseStyles} bg-red-100 text-red-800 border border-red-200`
            case "BooleanOperator":
                return `${baseStyles} bg-indigo-100 text-indigo-800 border border-indigo-200`
            case "Expression":
                return `${baseStyles} bg-gray-100 text-gray-800 border border-gray-200`
            default:
                return `${baseStyles} bg-gray-100 text-gray-800 border border-gray-200`
        }
    }

    return (
        <span
            {...attributes}
            className={`
                ${getBlockStyles()}
                ${isSelected ? 'ring-2 ring-blue-400 ring-opacity-50 shadow-md' : 'hover:shadow-sm'}
            `}
            contentEditable={false}
            data-cy={`mention-${element.character.replace(/\s/g, '-')}`}
        >
            <div contentEditable={false} className="flex items-center gap-1">
                {IS_MAC ? (
                    <Fragment>
                        {children}
                        <span>{element.character}</span>
                    </Fragment>
                ) : (
                    <Fragment>
                        <span>{element.character}</span>
                        {children}
                    </Fragment>
                )}
            </div>
        </span>
    )
}