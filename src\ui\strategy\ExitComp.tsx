import {Action, ActionType, Entry, Exit} from "../../types/strategy/Actions.ts";
import {ReactNode, useMemo, useState} from "react";
import {Target} from "../../types/strategy/Target.ts";
import {<PERSON>ton, Card, Modal, Select, Space, Tag, Typography} from "antd";
import {DeleteOutlined, PlusOutlined} from "@ant-design/icons";
import {AutosizeInputWithEditIcon} from "../../components/AutosizeInputWithEditIcon.tsx";
import {TargetTable} from "./Targets.tsx";
import {StudyParamSelector} from "../../components/StudyParamSelector.tsx";
import {TargetTransfer} from "../../components/TargetTransfer.tsx";
import {useMainSelector} from "../../redux/MainStore.ts";

type BaseCompProps<T extends ActionType> = {
    action: Action<T>,
    onChange: (action: Action<T>) => void,
    entryTargets: Target[],
    onRemove: () => void
    tag: ReactNode,
    content: ReactNode,
}

function BaseComp<T extends ActionType>(props: BaseCompProps<T>) {

    // const [modalProps, setModalProps] = useState<TargetEditorProps | null>(null)
    const [isModalOpen, setIsModalOpen] = useState(false)

    // entryTargets should be referenced targets
    // exit targets should be referenced targets

    const [selectedTargets, setSelectedTargets] = useState<Target[]>([])

    // const modifyTarget = (t: Target, idx: number) => {
    //     setModalProps({
    //         isModalOpen: true,
    //         initialTarget: t,
    //         onCancel(): void {
    //             setModalProps(null)
    //         },
    //         onOk(target: Target): void {
    //             setModalProps(null)
    //             const newTargets = [...props.action.targets]
    //             newTargets[idx] = target
    //             props.onChange({
    //                 ...props.action,
    //                 targets: newTargets
    //             })
    //         }
    //     })
    // }

    return (
        <>
            <Card size="small"
                  extra={
                      <Button onClick={props.onRemove} icon={<DeleteOutlined/>} shape={"circle"} type={"text"} color={"danger"} variant={"text"}></Button>
                  }
                  title={(
                      <div className={"flex items-center "}>
                          {
                              props.tag
                          }
                          <AutosizeInputWithEditIcon value={props.action.name} onChange={(name) => {
                              props.onChange({...props.action, name})
                          }}/>
                      </div>
                  )}
                  style={{width: "100%", minHeight: "320px"}}>
                <Space direction={"vertical"} style={{width: "100%"}}>

                    {
                        props.content
                    }
                    <div className="h-1"></div>
                    <Typography.Text type={"secondary"}>Targets</Typography.Text>
                    <TargetTable targets={selectedTargets} onTargetClicked={() => {}}/>
                    <div className={""}></div>
                    <Button icon={<PlusOutlined/>} type={"link"} size={"small"} onClick={() => {setIsModalOpen(true)}}>Add Target</Button>
                    <Modal title={"Add Targets"} width={1000} open={isModalOpen} onCancel={() => setIsModalOpen(false)} onOk={() => setIsModalOpen(false)}>
                        <TargetTransfer onTargetsChanged={setSelectedTargets} allTargets={props.entryTargets}/>
                    </Modal>
                </Space>
            </Card>
        </>
    )
}

type ExitCompProps = {
    // entryIds: Id[],
    // entryTargets: Target[],
    exit: Exit,
    onChange: (exit: Exit) => void,
    onRemove: () => void
}
export function ExitComp(props: ExitCompProps){

    const actions = useMainSelector(st => st.strategy.actions)
    const entries = useMemo(() => {
        return actions.filter(a => a.type === "Entry") as Entry[]
    }, [actions]);
    const selectedEntry = useMemo(() => {
        return entries.find(e => e._id === props.exit.forEntry)! // TODO: Not null assertion
    }, [entries, props.exit]);

    return (
        <BaseComp<"Exit">
            entryTargets={selectedEntry.targets}
            action={props.exit} onChange={props.onChange} onRemove={props.onRemove}
            tag={<Tag color={"orange"}>Exit</Tag>}
            content={
                <Space direction={"vertical"}>
                    <Space>
                        <Typography.Text type={"secondary"}>Exit Condition</Typography.Text>
                        <StudyParamSelector/>
                    </Space>
                    <Space>
                        <Typography.Text type={"secondary"}>For Entry</Typography.Text>
                        <Select options={entries.map(e => ({label: e.name, value: e._id}))} value={selectedEntry._id} onChange={(dt) => {
                            props.onChange({...props.exit, forEntry: dt})
                        }}>
                        </Select>
                    </Space>

                </Space>
            }/>
    )
}
