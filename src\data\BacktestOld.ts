import {useEffect, useState} from "react";
import {ChartElementsParserX, ChartElements} from "../utils/ChartElements.ts";
import {StrategyState} from "../redux/strategy/Strategy.ts";
import {parseLogs, StrategyLog} from "../utils/LogParser.ts";
import {SampleBacktestData} from "../utils/SampleBacktestData.ts";

export type BacktestApiResponse = {
    closedTrades: number,
    profitableTrades: number,
    profitPercent: number,
    overallPnl: number,
    maxDrawdown: number,
    avgProfit: number,
    avgLoss: number,
    chartElements: ChartElements,
    actionChartElements: ChartElements,
    closedPositions: {
        actionId: string,
        actionInstanceId: string,
        positions: {
            actionId: string,
            actionInstanceId: string,
            price: number,
            qty: number,
            targetId: string,
            token: string,
            tradeTime: number
        }[],
        cumulativePnl: number,
        timeTaken: number
    }[],
    logs: StrategyLog[]
}

export function useBacktestApi(strategy: StrategyState | undefined){
    const [data, setData] = useState<BacktestApiResponse | undefined>(undefined) // TODO: Use constructor

    useEffect(() => {
        async function run(){
            let data = SampleBacktestData as any
            try {
                // const dt = await fetch("http://192.168.1.5:8080/backtest", {
                const dt = await fetch("http://127.0.0.1:8080/backtest", {
                    method: "GET",
                    // headers: {
                    //     "Content-Type": "application/json",
                    // },
                    // body: JSON.stringify(strategy)
                })
                data = await dt.json()
            }
            catch (e){
                console.log(`Using sample data. Cannot fetch data: ${e}`)
            }
            const parser = new ChartElementsParserX(data.headers);
            const res2 = parser.parse(data.data)
            console.log("Parsed response", res2)

            console.log("a headers", data.actionDataHeaders)
            const actionParser = new ChartElementsParserX(data.actionDataHeaders);
            const actionChartElements = actionParser.parseWithoutTime(data.actionData)
            console.log("Action Chart Elements", actionChartElements)

            const logs = parseLogs(data.logs)
            setData({
                profitableTrades: data.profitableTrades,
                profitPercent: data.profitPercent,
                chartElements: res2,
                closedTrades: data.closedTrades,
                overallPnl: data.overallPnl,
                maxDrawdown: data.maxDrawdown,
                avgProfit: data.avgProfit,
                avgLoss: data.avgLoss,
                logs: logs,
                closedPositions: data.closedPositions,
                actionChartElements
            })
        }
        run()
    }, []);

    return data;
}



/*
 * Primary (OHLC)
 * Strategy -> Actions (Entry, Exit, SL, SL lines) (Lines and Markers)
 * Trade -> Pnl, Drawdown
 * Orders -> Placement (Markers)
 * Study -> (Lines, Markers)
 * */

// min pnl to max pnl (y axis)
// categories - 25,50,75,90