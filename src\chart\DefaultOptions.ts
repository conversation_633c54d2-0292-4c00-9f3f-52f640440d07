import {CrosshairMode, DeepPartial, TimeChartOptions} from "lightweight-charts";


export const DefaultChartOptions: DeepPartial<TimeChartOptions> = {
    watermark: {
       visible: false
    },
    layout: {
        background:{
            color: 'transparent',
        },
        // textColor: darkMode ? '#c9d0d4' : '#343C44',
        fontFamily: '"Inter var", sans-serif',
    },
    leftPriceScale: {
        visible: false,
        // borderColor: darkMode ? 'rgb(63,63,70)' : '#c9d0d4',
    },
    rightPriceScale: {
        visible: true,
        // borderColor: darkMode ? 'rgb(63,63,70)' : '#c9d0d4',
    },
    timeScale: {
        timeVisible: true,
        secondsVisible: false,
        visible: true,
        // borderColor: darkMode ? 'rgb(63,63,70)' : '#c9d0d4',
    },
    // localization: {
    //     timeFormatter: (time: Time) => {
    //         if (typeof time == "string")
    //             return time;
    //         return formatter.format((time as UTCTimestamp) * 1000);
    //     }
    // },
    grid: {
        vertLines: {visible:false},
        horzLines: {visible:false}
    },
    crosshair: {
        mode: CrosshairMode.Normal,
        // vertLine: {
        //     labelBackgroundColor: "#343C44"
        // },
        // horzLine: {
        //     labelBackgroundColor: "#343C44"
        // }
    }
}
