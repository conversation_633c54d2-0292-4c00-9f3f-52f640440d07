import {Button, ConfigProvider, Input, InputRef} from "antd";
import {EditOutlined} from "@ant-design/icons";
import React, {useCallback, useEffect, useMemo, useRef, useState} from "react";

const DefaultPaddingSizePx = 48
const DefaultPaddingSizeCh = 1

// TODO: Padding sizes (ch and px)
// TODO: Antd input props
type AutosizeInputProps = {
    value: string,
    onChange: (value: string) => void
    // paddingSize?: number,
}

export function AutosizeInputWithEditIcon(props: AutosizeInputProps) {
    const inputRef = useRef<InputRef>(null);
    const [disabled, setDisabled] = useState(true)

    const inputLength = useMemo(() => {
        return props.value.length
    }, [props.value]);

    const onChange: React.ChangeEventHandler<HTMLInputElement> = useCallback((e) => {
        props.onChange(e.target.value)
    }, [props]);

    useEffect(() => {
        if (!disabled)
            inputRef.current?.focus({cursor: "end"})
    }, [disabled]);

    return (
        <ConfigProvider theme={{
            token: {
                colorTextDisabled: "rgba(0,0,0, 0.88)",
                colorText: "#4096ff"
            },
            components: {
                Button: {
                    textHoverBg: "transparent",
                    colorBgTextActive: "transparent",
                }
            }
        }}>
            <Input ref={inputRef}
                   onBlur={() => setDisabled(true)}
                   value={props.value}
                   disabled={disabled}
                   style={{width: `calc(${inputLength+DefaultPaddingSizeCh}ch + ${DefaultPaddingSizePx}px)`, cursor: "default"}}
                   onChange={onChange}
                   variant="borderless"
                   suffix={ <Button onClick={() => {
                    setDisabled(false)
                   }}    icon={disabled ? <EditOutlined/> : undefined} shape={"circle"} type={"link"}/>}
            />

        </ConfigProvider>
    )
}