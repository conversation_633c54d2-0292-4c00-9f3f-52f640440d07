@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;

  color-scheme: light dark;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


body {
  margin: 0;
  /*display: flex;*/
  background-color: #f5f5f5;
}

.flexible-card .ant-card-body {
  flex-grow: 1
}

/*for full size antd tab content*/
.ant-tabs-content {
  height: 100%;
}
.ant-tabs-tabpane {
  height: 100%;
}
.ant-tabs-tabpane-active {
  height: 100%;
}

/*::-webkit-scrollbar {*/
/*  width: 0px;*/
/*  background: transparent; !* make scrollbar transparent *!*/
/*}*/