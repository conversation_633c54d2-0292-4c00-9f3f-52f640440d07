import {Broke<PERSON>, Broke<PERSON>} from "./Broker.ts";
import {Select} from "@radix-ui/themes";

type SelectBrokerProps = {
    brokerId: Broker,
    setSelectedBroker: (broker: Broker) => void,
}

export function SelectBroker(props: SelectBrokerProps){
    return (
        <Select.Root size="2" value={props.brokerId.id} onValueChange={(id) => {
            props.setSelectedBroker(Brokers.getById(id));
        }}>
            <Select.Trigger />
            <Select.Content>
                {
                    Brokers.all.map(b =>
                        <Select.Item value={b.id}>{b.name}</Select.Item>
                    )
                }
            </Select.Content>
        </Select.Root>
    )
}