import {StudyType, StudyTypeValue} from "../study/StudyType";
import {ReadStudyParam} from "../study/ReadStudyParam";

/**
 * A type of configurable value that represents a constant value.
 * */
export type Constant<S extends StudyType> = StudyTypeValue<S>

/**
 * A type of configurable value that represents a value take from a study calculation.
 * */
export type Study<S extends StudyType> = ReadStudyParam<S>

/**
 * A type map for configurable value types to their respective values.
 * CONFIGURABLE
 *  Add a new configurable value type here.
 * */
export interface ConfigTypeToValueMap<S extends StudyType = StudyType> {
    Constant: Constant<S>,
    Study: Study<S>
}

/**
 * Types of configurable values.
 * */
export type ConfigurableValueType = keyof ConfigTypeToValueMap