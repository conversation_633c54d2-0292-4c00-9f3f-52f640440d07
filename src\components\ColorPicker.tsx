import * as React from "react";
import * as Popover from "@radix-ui/react-popover";
import { BlockPicker } from "react-color";

interface ColorPickerProps {
    color: string;
    setColor: (color: string) => void;
    width?: number;
    height?: number;
    colors?: string[];
}

const ColorPicker: React.FC<ColorPickerProps> = ({ color, setColor, width, height, colors }) => {
    const [open, setOpen] = React.useState(false);

    return (
        <Popover.Root open={open} onOpenChange={setOpen}>
            <Popover.Trigger asChild>
                <button
                    className={`rounded-md ${width === undefined && height === undefined ? 'w-8 h-8' : ''}`}
                    style={{
                        backgroundColor: color,
                        ...(width !== undefined && { width: `${width}px` }),
                        ...(height !== undefined && { height: `${height}px` }),
                    }}
                    aria-label="Open color picker"
                />
            </Popover.Trigger>
            <Popover.Portal>
                <Popover.Content
                    className="bg-white rounded-md shadow-lg p-2"
                    side="right"
                    sideOffset={5}
                >
                    <BlockPicker
                        color={color}
                        onChangeComplete={(color) => {
                            setColor(color.hex);
                            setOpen(false);
                        }}
                        triangle="hide"
                        colors={colors}
                    />
                </Popover.Content>
            </Popover.Portal>
        </Popover.Root>
    );
};

export default ColorPicker;