import React from 'react';

interface StatChipProps {
    label: string;
    value: string | number | React.ReactNode;
    variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
    size?: 'sm' | 'md' | 'lg';
    radius?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

export const StatChip: React.FC<StatChipProps> = ({
                                               label,
                                               value,
                                               variant = 'default',
                                               size = 'md',
                                               radius = 'md'
                                           }) => {
    const variantClasses = {
        default: 'bg-gray-100 text-gray-800 border-gray-200',
        success: 'bg-green-100 text-green-800 border-green-200',
        warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        error: 'bg-red-100 text-red-800 border-red-200',
        info: 'bg-blue-100 text-blue-800 border-blue-200'
    };

    const sizeClasses = {
        sm: 'px-2 py-1 text-xs',
        md: 'px-3 py-1.5 text-sm',
        lg: 'px-4 py-2 text-base'
    };

    const radiusClasses = {
        none: 'rounded-none',
        sm: 'rounded-sm',
        md: 'rounded-md',
        lg: 'rounded-lg',
        xl: 'rounded-xl',
        full: 'rounded-full'
    };

    return (
        <div className={`
      inline-flex items-center gap-2 border font-medium
      ${variantClasses[variant]}
      ${sizeClasses[size]}
      ${radiusClasses[radius]}
    `}>
            <span className="text-opacity-70">{label}:</span>
            <span className="font-semibold">{value}</span>
        </div>
    );
};
