import {
    BitmapCoordinatesR<PERSON>ingScope,
    CanvasRenderingTarget2D,
} from 'fancy-canvas';
import {
    CustomData,
    ICustomSeriesPaneRenderer,
    PriceToCoordinateConverter,
} from 'lightweight-charts';

export interface FillData extends CustomData {
    value1: number;
    value2: number;
}

interface FillBarItem {
    x: number;
    value1: number;
    value2: number;
}

import {
    CustomSeriesOptions,
    customSeriesDefaultOptions,
} from 'lightweight-charts';
import {
    CustomSeriesPricePlotValues,
    ICustomSeriesPaneView,
    PaneRendererCustomData,
    WhitespaceData,
    Time,
} from 'lightweight-charts';

export class FillSeriesPlugin<TData extends FillData>
    implements ICustomSeriesPaneView<Time, TData, FillSeriesOptions>
{
    _renderer: FillSeriesRenderer<TData>;

    constructor() {
        this._renderer = new FillSeriesRenderer();
    }

    priceValueBuilder(plotRow: TData): CustomSeriesPricePlotValues {
        return [plotRow.value1, plotRow.value2];
    }

    isWhitespace(data: TData | WhitespaceData): data is WhitespaceData {
        return (data as Partial<TData>).value1 === undefined || (data as Partial<TData>).value2 === undefined;
    }

    renderer(): FillSeriesRenderer<TData> {
        return this._renderer;
    }

    update(
        data: PaneRendererCustomData<Time, TData>,
        options: FillSeriesOptions
    ): void {
        this._renderer.update(data, options);
    }

    defaultOptions() {
        return defaultOptions;
    }
}

export interface FillSeriesOptions extends CustomSeriesOptions {
    fillColor: string;
    fillOpacity: number;
    isVisible: boolean;
}

export const defaultOptions: FillSeriesOptions = {
    ...customSeriesDefaultOptions,
    fillColor: 'rgba(4, 153, 129)',
    fillOpacity: 0.3,
    isVisible: true,
} as const;

export class FillSeriesRenderer<TData extends FillData>
    implements ICustomSeriesPaneRenderer
{
    _data: PaneRendererCustomData<Time, TData> | null = null;
    _options: FillSeriesOptions | null = null;

    draw(
        target: CanvasRenderingTarget2D,
        priceConverter: PriceToCoordinateConverter
    ): void {
        target.useBitmapCoordinateSpace(scope =>
            this._drawImpl(scope, priceConverter)
        );
    }

    update(
        data: PaneRendererCustomData<Time, TData>,
        options: FillSeriesOptions
    ): void {
        this._data = data;
        this._options = options;
    }

    _drawImpl(
        renderingScope: BitmapCoordinatesRenderingScope,
        priceToCoordinate: PriceToCoordinateConverter
    ): void {
        if (
            this._data === null ||
            this._data.bars.length === 0 ||
            this._data.visibleRange === null ||
            this._options === null ||
            !this._options.isVisible
        ) {
            return;
        }

        const options = this._options;
        const bars: FillBarItem[] = this._data.bars.map(bar => {
            return {
                x: bar.x * renderingScope.horizontalPixelRatio,
                value1: priceToCoordinate(bar.originalData.value1)! * renderingScope.verticalPixelRatio,
                value2: priceToCoordinate(bar.originalData.value2)! * renderingScope.verticalPixelRatio,
            };
        });

        const ctx = renderingScope.context;

        // Create the filled area between value1 and value2
        const area = new Path2D();
        const firstBar = bars[this._data.visibleRange.from];

        // Start with value1 line
        area.moveTo(firstBar.x, firstBar.value1);
        for (
            let i = this._data.visibleRange.from + 1;
            i < this._data.visibleRange.to;
            i++
        ) {
            const bar = bars[i];
            area.lineTo(bar.x, bar.value1);
        }

        // Connect to value2 line and draw it in reverse
        const lastBar = bars[this._data.visibleRange.to - 1];
        area.lineTo(lastBar.x, lastBar.value2);
        for (
            let i = this._data.visibleRange.to - 2;
            i >= this._data.visibleRange.from;
            i--
        ) {
            const bar = bars[i];
            area.lineTo(bar.x, bar.value2);
        }

        // Close the path
        area.closePath();

        // Fill the area
        ctx.fillStyle = options.fillColor;
        ctx.globalAlpha = options.fillOpacity
        ctx.fill(area);
    }
}