import { configureStore } from '@reduxjs/toolkit'
import {StrategySlice} from "./strategy/Strategy.ts";
import {useDispatch, useSelector} from "react-redux";

export const MainStore = configureStore({
    reducer: {
        strategy: StrategySlice.reducer,
    },
})

export type RootState = ReturnType<typeof MainStore.getState>
export type AppDispatch = typeof MainStore.dispatch

export const useMainDispatch = useDispatch.withTypes<AppDispatch>()
export const useMainSelector = useSelector.withTypes<RootState>()