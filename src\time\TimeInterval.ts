export type TimeInterval = {
    start: number;
    endExclusive: number;
}

export class TimeIntervalCalculator {

    readonly intervalSeconds: number;

    constructor(intervalSeconds: number) {
        this.intervalSeconds = intervalSeconds;
    }

    calculateInterval(alignedFrom: number, timePoint: number): TimeInterval {
        const intervalIndex = Math.floor((timePoint - alignedFrom) / this.intervalSeconds);
        const start = alignedFrom + intervalIndex * this.intervalSeconds;
        const endExclusive = start + this.intervalSeconds;
        return { start, endExclusive };
    }
}