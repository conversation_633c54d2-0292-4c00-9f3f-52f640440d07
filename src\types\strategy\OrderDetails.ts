import {ValueOrPercent} from "../ValueOrPercent.ts";

export const OrderSide = {
    Buy: "Buy",
    Sell: "Sell"
}
export type OrderSide = "Buy" | "Sell"
export type Quantity = {
    type: "Lots" | "Qty",
    value: number
}
export const QuantityTypesList: Quantity["type"][] = ["Lots", "Qty"]
export type OrderValidity = "Day" | "IOC"
export const OrderValidityList: OrderValidity[] = ["Day", "IOC"]

export type OrderType = {
    type: "Market",
} | {
    type: "Limit",
    value: ValueOrPercent,
}

export const OrderType = {
    getShortDisplayName: (order: OrderType): string => {
        if (order.type === "Market") return "Mkt"
        else return ValueOrPercent.getSignedDisplayName(order.value)
    },
    getDisplayName: (order: OrderType): string => {
        if (order.type === "Market") return "Market Order"
        else {
            return `Limit Order ${ValueOrPercent.getSignedDisplayName(order.value)}`
        }
    }
}
export const OrderValidity = {
    getDisplayName: (v: OrderValidity): string => {
        return `${v} Validity`;
    }
}

export type OrderDetails = {
    side: OrderSide,
    quantity: Quantity,
    validity: OrderValidity,
    orderType: OrderType
}