import {URL_PATH_INSTRUMENTS} from "../constants.ts";
import { BrokerId } from "./brokers/Broker.ts";
import {undefined, z} from "zod";

const InstrumentTypeSchema = z.enum(["INDEX", "EQ", "FUT", "CE", "PE"])

const InstrumentSchema = z.object({
    type: InstrumentTypeSchema,
    id: z.string(),
    symbol: z.string(),
    exchange: z.string(),
    token: z.string(),
    name: z.string(),
    brokerId: z.string().optional() // TODO: remove optional
});

export const DefaultInstrument: Instrument = {
    exchange: "NSE",
    id: "NSE:HDFCBANK:EQ",
    name: "HDFC BANK LTD",
    symbol: "HDFCBANK",
    token: "NSE:HDFCBANK-EQ",
    type: "EQ",
}

const InstrumentsResponseSchema = z.array(InstrumentSchema);

export type InstrumentType = z.infer<typeof InstrumentTypeSchema>
export type Instrument = z.infer<typeof InstrumentSchema>;

export async function queryInstruments(broker: BrokerId, query: string, filter?: InstrumentType[]): Promise<Instrument[]> {
    let obj: Record<string, string> = { query };
    if (filter !== undefined) {
        obj = {
            ...obj,
            filter: filter.join(",")
        }
    }
    const params = new URLSearchParams(obj);

    const res = await fetch(`${URL_PATH_INSTRUMENTS}/${broker}?${params.toString()}`);

    const data = await res.json();
    return InstrumentsResponseSchema.parse(data); // will throw if invalid
}