import {useEffect, useMemo, useRef, useState} from "react";
import {
    ConfigProvider,
    Divider,
    Input,
    InputNumber,
    InputRef,
    Modal,
    Radio,
    Segmented,
    Select,
    Space,
    Steps,
    Typography
} from "antd";
import {LabelledControl} from "./LabelledControl.tsx";
import {parseToNumber} from "./TargetSelector.tsx";
import {QuantityInput} from "./CascaderNumberInput.tsx";
import {OrderTypeInput} from "./OrderTypeInput.tsx";
import {Target} from "../types/strategy/Target.ts";
import {TickerType, TickerTypesList} from "../types/strategy/Ticker.ts";
import {useImmer} from "use-immer";
import {
    Constructors,
    ExpiryType,
    ExpiryTypesList,
    InstrumentType,
    isDerivative
} from "../types/strategy/InstrumentType.ts";
import {OrderSide, OrderType, OrderValidity, OrderValidityList, Quantity} from "../types/strategy/OrderDetails.ts";

export type TargetEditorProps = {
    isModalOpen: boolean,
    initialTarget: Target,
    onCancel: () => void,
    onOk: (target: Target) => void
}

export function TargetEditor(props: TargetEditorProps) {
    // Modal & Steps
    const {isModalOpen} = props
    const handleOk = () => {
        if (current === 2)
        {
            // TODO: call onOK
            props.onOk(target)
        }
            // setIsModalOpen(false);
        else setCurrent(current + 1)
    };
    const [current, setCurrent] = useState(0)

    // Target
    const [target, setTarget] = useImmer<Target>(props.initialTarget);

    // Ticker Step
    const tickerInputRef = useRef<InputRef>(null);
    const setTickerType = (type: TickerType) => {
        // TODO: Use constructor
        setTarget(draft => {
            draft.ticker.type = type
        })
    }
    // Focus Ticker search input
    useEffect(() => {
        if (target.ticker.type === "Custom")
            tickerInputRef.current?.focus()
    }, [target.ticker.type]);


    // Instrument Step
    const setInstType = (type: InstrumentType) => {
        setTarget(draft => {
            // TODO: Create instrument from selected ticker
            draft.instrument = Constructors.DefaultInstrument(type)
        })
    }
    const setExpiryType = (type: ExpiryType) => {
        setTarget(draft => {
            if (isDerivative(draft.instrument))
                draft.instrument.value.expiry = type
        })
    }
    const setExpiryOffset = (offset: number | null) => {
        setTarget(draft => {
            if (isDerivative(draft.instrument))
                draft.instrument.value.expiryOffset = offset ?? 0 // TODO: Use a constant for default expiry offset
        })
    }
    // TODO: Strike
    // const [strike, setStrike] = useState<number | null>(1)
    // const [strikeType, setStrikeType] = useState<"ATM" | "ITM" | "OTM">("ATM")
    const expiryStyle = useMemo(() => {
        return target.instrument.type === "Spot" ? {display: "none"} : {}
    }, [target.instrument.type]);

    const setOrderSide = (side: OrderSide) => {
        setTarget(draft => {
            draft.orderDetails.side = side
        })
    }
    const setOrderValidity = (validity: OrderValidity) => {
        setTarget(draft => {
            draft.orderDetails.validity = validity
        })
    }
    const setQuantity = (quantity: Quantity | null) => {
        setTarget(draft => {
            draft.orderDetails.quantity = quantity ?? { type: "Lots", value: 1 } // TODO: Use constant for default qty
        })
    }
    const setOrderType = (orderType: OrderType) => {
        setTarget(draft => {
            draft.orderDetails.orderType = orderType
        })
    }

    return (
        <>
            <Modal destroyOnClose={true} okText={current === 2 ? "OK" : "Next"} open={isModalOpen} onOk={handleOk} onCancel={props.onCancel}>
                <div>
                    <div className="h-8"></div>
                    <Steps
                        progressDot
                        current={current}
                        onChange={setCurrent}
                        items={[
                            {
                                title: 'Select Ticker',
                            },
                            {
                                title: 'Instrument Type',
                            },
                            {
                                title: 'Order Details',
                            },
                        ]}
                    />
                    <Divider/>
                    <div className={"min-h-80 flex flex-col px-4"}>
                        {
                            current === 0 &&
                            <LabelledControl title={"Select Ticker"}>
                                <Space>
                                    <Segmented<TickerType>
                                        value={target.ticker.type}
                                        options={TickerTypesList}
                                        onChange={setTickerType}
                                    />
                                    <Input ref={tickerInputRef} placeholder={"Search ticker..."}
                                           disabled={target.ticker.type === "Chart"}/>
                                </Space>
                            </LabelledControl>
                        }
                        {
                            current === 1 &&
                            <Space direction={"vertical"} size={"middle"}>
                                <Space direction={"vertical"}>
                                    <Typography.Text>Instrument</Typography.Text>
                                    <Segmented<InstrumentType>
                                        value={target.instrument.type}
                                        options={['Spot', 'Future', 'CE', "PE"]}
                                        onChange={setInstType}
                                    />
                                </Space>
                                {
                                    isDerivative(target.instrument) &&
                                    // target.instrument.type !== "Spot" &&
                                    <Space>
                                        <Space style={expiryStyle} direction={"vertical"}>
                                            <Typography.Text>Expiry</Typography.Text>
                                            <InputNumber
                                                addonBefore={
                                                    <Select<ExpiryType>
                                                        value={target.instrument.value.expiry}
                                                        onChange={setExpiryType}
                                                    >
                                                        {
                                                            ExpiryTypesList.map(t =>
                                                                <Select.Option key={t}>{t}</Select.Option>
                                                            )
                                                        }
                                                    </Select>
                                                }
                                                parser={parseToNumber}
                                                formatter={(value) => {
                                                    // @ts-expect-error - value can be a string, issue is related to antd component
                                                    return (value !== undefined && value !== 0 && value !== "0") ? `Next +${value}` : "Next"
                                                }}
                                                style={{width: "180px"}}
                                                keyboard
                                                changeOnWheel
                                                value={target.instrument.value.expiryOffset}
                                                onChange={setExpiryOffset}
                                                min={0}
                                                max={10}
                                            />
                                        </Space>
                                    </Space>
                                }
                                {/*// TODO: Selection criteria should have an option - Recalculate strike - when strategy starts, on first entry, on each entry  */}
                                {/*{*/}
                                {/*    instType !== "Spot" &&*/}
                                {/*    <Space style={typeStyle} direction={"vertical"}>*/}
                                {/*        <Typography.Text>{"Strike"}</Typography.Text>*/}
                                {/*        {*/}
                                {/*            <InputNumber*/}
                                {/*                style={{width: "130px"}}*/}
                                {/*                addonBefore={*/}
                                {/*                    <Select value={strikeType} onSelect={setStrikeType}>*/}
                                {/*                        <Select.Option key={"ATM"}>ATM</Select.Option>*/}
                                {/*                        <Select.Option key={"OTM"}>OTM</Select.Option>*/}
                                {/*                        <Select.Option key={"ITM"}>ITM</Select.Option>*/}
                                {/*                    </Select>*/}
                                {/*                }*/}
                                {/*                keyboard*/}
                                {/*                changeOnWheel*/}
                                {/*                min={1}*/}
                                {/*                max={20}*/}
                                {/*                value={strike}*/}
                                {/*                onChange={setStrike}*/}
                                {/*                disabled={strikeType === "ATM"}*/}
                                {/*            />*/}
                                {/*        }*/}
                                {/*    </Space>*/}
                                {/*}*/}
                            </Space>
                        }
                        {
                            current === 2 &&
                            <>
                                <Space direction={"vertical"} size={"middle"}>
                                    <Space size={"large"}>
                                        <Space direction={"vertical"}>
                                            <Typography.Text>Side</Typography.Text>
                                            {/*// TODO: Radio button text hover color, divider color*/}
                                            <Radio.Group
                                                value={target.orderDetails.side}
                                                onChange={(e) => { setOrderSide(e.target.value) }}
                                                buttonStyle="solid">
                                                <ConfigProvider theme={{
                                                    components: {
                                                        Radio: {
                                                            buttonSolidCheckedBg: "#108ee9",
                                                            buttonSolidCheckedActiveBg: "#108ee9",
                                                            buttonSolidCheckedHoverBg: "#108ee9"
                                                        }
                                                    }
                                                }}>
                                                    <Radio.Button value={OrderSide.Buy}>BUY</Radio.Button>
                                                </ConfigProvider>
                                                <ConfigProvider theme={{
                                                    components: {
                                                        Radio: {
                                                            buttonSolidCheckedBg: "#f50",
                                                            buttonSolidCheckedActiveBg: "#f50",
                                                            buttonSolidCheckedHoverBg: "#f50"

                                                        }
                                                    }
                                                }}>
                                                    <Radio.Button value={OrderSide.Sell}>SELL</Radio.Button>
                                                </ConfigProvider>
                                            </Radio.Group>
                                        </Space>
                                        <div className="w-8"></div>
                                        <Space direction={"vertical"}>
                                            <Typography.Text>Quantity</Typography.Text>
                                            {/*// TODO: Radio button text hover color, divider color*/}
                                            <QuantityInput quantity={target.orderDetails.quantity} onChange={setQuantity}/>
                                        </Space>
                                    </Space>


                                    <Space className={"flex-1"} direction={"vertical"}>
                                        <Typography.Text>Order Validity</Typography.Text>
                                        <Segmented<OrderValidity>
                                            value={target.orderDetails.validity}
                                            onChange={setOrderValidity}
                                            options={OrderValidityList}/>
                                    </Space>
                                    <Space className={"flex-1"} direction={"vertical"}>
                                        <Typography.Text>Order Type</Typography.Text>
                                        <OrderTypeInput orderType={target.orderDetails.orderType} onChange={setOrderType}/>
                                    </Space>
                                </Space>

                            </>
                        }
                    </div>
                </div>
            </Modal>
        </>
    );
}
