import { CanvasRenderingTarget2D } from 'fancy-canvas';
import {
    AutoscaleInfo,
    BarData,
    Coordinate,
    DataChangedScope,
    ISeriesPrimitive,
    ISeriesPrimitivePaneRenderer,
    ISeriesPrimitivePaneView,
    LineData,
    SeriesAttachedParameter,
    SeriesDataItemTypeMap,
    SeriesMarker,
    SeriesType,
    Time,
} from 'lightweight-charts';
import {PluginBase} from "./PluginBase.ts";
import {ClosestTimeIndexFinder} from "./helpers/closest-index.ts";
import {cloneReadonly} from "./helpers/simple-clone.ts";

interface MarkerRendererData {
    x: Coordinate | number;
    y: Coordinate | number;
    marker: SeriesMarker<Time>;
}

class MarkersIndicatorPaneRenderer implements ISeriesPrimitivePaneRenderer {
    _viewData: MarkerViewData;

    constructor(data: MarkerViewData) {
        this._viewData = data;
    }

    draw() {}

    drawBackground(target: CanvasRenderingTarget2D) {
        const markers: MarkerRendererData[] = this._viewData.data;

        target.useBitmapCoordinateSpace(scope => {
            const ctx = scope.context;
            ctx.scale(scope.horizontalPixelRatio, scope.verticalPixelRatio);

            markers.forEach(markerData => {
                const { x, y, marker } = markerData;

                // Set marker styles
                ctx.fillStyle = marker.color || this._viewData.options.defaultColor;
                ctx.strokeStyle = marker.color || this._viewData.options.defaultColor;
                ctx.lineWidth = this._viewData.options.lineWidth;

                // Draw marker based on position
                const size = marker.size || this._viewData.options.defaultSize;
                // const halfSize = size / 2;

                ctx.save();

                // Draw marker shape
                if (marker.position === 'aboveBar') {
                    this._drawMarker(ctx, x, y - this._viewData.options.offset, size, marker.shape || 'circle');
                } else if (marker.position === 'belowBar') {
                    this._drawMarker(ctx, x, y + this._viewData.options.offset, size, marker.shape || 'circle');
                } else {
                    // inBar position
                    this._drawMarker(ctx, x, y, size, marker.shape || 'circle');
                }

                // Draw text if provided
                if (marker.text) {
                    ctx.fillStyle = marker.color || this._viewData.options.defaultColor;
                    ctx.font = `${this._viewData.options.fontSize}px ${this._viewData.options.fontFamily}`;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';

                    let textY = y;
                    if (marker.position === 'aboveBar') {
                        textY = y - this._viewData.options.offset - size - this._viewData.options.textOffset;
                    } else if (marker.position === 'belowBar') {
                        textY = y + this._viewData.options.offset + size + this._viewData.options.textOffset;
                    }

                    ctx.fillText(marker.text, x, textY);
                }

                ctx.restore();
            });
        });
    }

    private _drawMarker(ctx: CanvasRenderingContext2D, x: number, y: number, size: number, shape: string) {
        const halfSize = size / 2;

        ctx.beginPath();

        switch (shape) {
            case 'circle':
                ctx.arc(x, y, halfSize, 0, 2 * Math.PI);
                ctx.fill();
                ctx.stroke();
                break;

            case 'square':
                ctx.rect(x - halfSize, y - halfSize, size, size);
                ctx.fill();
                ctx.stroke();
                break;

            case 'arrowUp':
                ctx.moveTo(x, y - halfSize);
                ctx.lineTo(x - halfSize, y + halfSize);
                ctx.lineTo(x + halfSize, y + halfSize);
                ctx.closePath();
                ctx.fill();
                ctx.stroke();
                break;

            case 'arrowDown':
                ctx.moveTo(x, y + halfSize);
                ctx.lineTo(x - halfSize, y - halfSize);
                ctx.lineTo(x + halfSize, y - halfSize);
                ctx.closePath();
                ctx.fill();
                ctx.stroke();
                break;

            default:
                // Default to circle
                ctx.arc(x, y, halfSize, 0, 2 * Math.PI);
                ctx.fill();
                ctx.stroke();
                break;
        }
    }
}

interface MarkerViewData {
    data: MarkerRendererData[];
    options: Required<MarkersIndicatorOptions>;
}

class MarkersIndicatorPaneView implements ISeriesPrimitivePaneView {
    _source: MarkersIndicator;
    _data: MarkerViewData;

    constructor(source: MarkersIndicator) {
        this._source = source;
        this._data = {
            data: [],
            options: this._source._options,
        };
    }

    update() {
        const series = this._source.series;
        const timeScale = this._source.chart.timeScale();

        // Get visible range
        const visibleRange = timeScale.getVisibleRange();
        if (!visibleRange) {
            this._data.data = [];
            return;
        }

        // Filter markers to only visible ones and convert coordinates
        this._data.data = this._source._markersData
            .filter(d => {
                const time = d.time as number;
                return time >= (visibleRange.from as number) && time <= (visibleRange.to as number);
            })
            .map(d => {
                const price = extractPrice(d.dataPoint);
                const x = timeScale.timeToCoordinate(d.time);
                const y = series.priceToCoordinate(price ?? 0);

                // Only include markers that have valid coordinates
                if (x === null || y === null) return null;

                return {
                    x,
                    y,
                    marker: d.marker,
                };
            })
            .filter((item): item is MarkerRendererData => item !== null);
    }

    renderer() {
        return new MarkersIndicatorPaneRenderer(this._data);
    }
}

interface MarkerData {
    time: Time;
    marker: SeriesMarker<Time>;
    dataPoint: SeriesDataItemTypeMap[SeriesType];
}

function extractPrice(
    dataPoint: SeriesDataItemTypeMap[SeriesType]
): number | undefined {
    if ((dataPoint as BarData).close) return (dataPoint as BarData).close;
    if ((dataPoint as LineData).value) return (dataPoint as LineData).value;
    return undefined;
}

export interface MarkersIndicatorOptions {
    defaultColor?: string;
    defaultSize?: number;
    lineWidth?: number;
    offset?: number;
    fontSize?: number;
    fontFamily?: string;
    textOffset?: number;
    customValuesProperty?: string;
}

const defaults: Required<MarkersIndicatorOptions> = {
    defaultColor: 'rgb(255, 82, 82)',
    defaultSize: 8,
    lineWidth: 1,
    offset: 10,
    fontSize: 12,
    fontFamily: 'Arial',
    textOffset: 8,
    customValuesProperty: 'markers',
};

export class MarkersIndicator extends PluginBase implements ISeriesPrimitive<Time> {
    _paneViews: MarkersIndicatorPaneView[];
    _seriesData: SeriesDataItemTypeMap[SeriesType][] = [];
    _markersData: MarkerData[] = [];
    _options: Required<MarkersIndicatorOptions>;
    _timeIndices: ClosestTimeIndexFinder<{ time: number }>;

    constructor(options: MarkersIndicatorOptions = {}) {
        super();
        this._options = { ...defaults, ...options };
        this._paneViews = [new MarkersIndicatorPaneView(this)];
        this._timeIndices = new ClosestTimeIndexFinder([]);
    }

    updateAllViews() {
        this._paneViews.forEach(pw => pw.update());
    }

    paneViews() {
        return this._paneViews;
    }

    attached(p: SeriesAttachedParameter<Time>): void {
        super.attached(p);
        this.dataUpdated('full');
    }

    dataUpdated(scope: DataChangedScope) {
        this._seriesData = cloneReadonly(this.series.data());
        this.extractMarkers();
        if (scope === 'full') {
            this._timeIndices = new ClosestTimeIndexFinder(
                this._seriesData as { time: number }[]
            );
        }
    }

    extractMarkers() {
        const markersData: MarkerData[] = [];

        this._seriesData.forEach(dataPoint => {
            // Check if the data point has custom values with markers
            const customValues = (dataPoint).customValues;
            if (customValues && customValues[this._options.customValuesProperty]) {
                const markers = customValues[this._options.customValuesProperty];

                // Handle both single marker and array of markers
                const markersArray = Array.isArray(markers) ? markers : [markers];

                markersArray.forEach((marker: SeriesMarker<Time>) => {
                    markersData.push({
                        time: dataPoint.time,
                        marker,
                        dataPoint,
                    });
                });
            }
        });

        this._markersData = markersData;
    }

    autoscaleInfo(): AutoscaleInfo | null {
        // Markers don't affect the price scale, so return null
        return null;
    }

    // Helper method to update marker options
    updateOptions(options: Partial<MarkersIndicatorOptions>) {
        this._options = { ...this._options, ...options };
        this.updateAllViews();
        this.requestUpdate();
    }

    // Helper method to get all markers for a specific time
    getMarkersAtTime(time: Time): SeriesMarker<Time>[] {
        return this._markersData
            .filter(d => d.time === time)
            .map(d => d.marker);
    }
}