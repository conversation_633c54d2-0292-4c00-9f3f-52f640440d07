import {Button, Space, Tooltip, Typography} from "antd";
import {InfoCircleOutlined} from "@ant-design/icons";
import React from "react";

type LabelledControlProps = {
    title: string,
    description?: string,
    iconToolTipText?: string,
    children?: React.ReactNode,
}

export function LabelledControl(props: LabelledControlProps) {
    return (
        <div>
            <div className={"mb-1"}>
                <Space align={"center"}>
                <Typography.Text style={{whiteSpace: 'nowrap'}}>
                    {props.title}
                </Typography.Text>
                <Tooltip title={
                    <span>
                        {props.iconToolTipText}
                        <span>
                            <Button size={"small"} type={"link"}>Learn more.</Button>
                        </span>
                    </span>
                }>
                    <Button icon={<InfoCircleOutlined/>} type={"text"} style={{}}></Button>
                </Tooltip>
                {/*<Switch className={""} size={'small'}/>*/}
                </Space>
            </div>

            {props.children}
            <div>
                <Typography.Text type={"secondary"} style={{whiteSpace: 'nowrap'}}>
                    {props.description}
                </Typography.Text>
            </div>
        </div>
    )
}