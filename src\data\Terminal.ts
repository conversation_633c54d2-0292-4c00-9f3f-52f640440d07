import {useEffect, useMemo, useRef, useState} from "react";
import {ChartElementDataItem, ChartElementHeaderWithStore, ChartElementsParser} from "../utils/ChartElementsParser.ts";
import useWebSocket from "react-use-websocket";
import {URL_SOCKET_TERMINAL} from "../constants.ts";
import {TimeSeriesStore} from "../utils/Store.ts";
import {MutableValueStore} from "../utils/ValueStore.ts";
import {checkTCMessageType, TCData, TCMessage} from "./StrategyTerminal.ts";
import {BrokerId} from "./brokers/Broker.ts";
import {SingleExpression} from "../components/condition/ConditionBuilder.tsx";
import {Expression} from "../xeno/CompositeValue.ts";
import {StudyParam} from "../types/study/ReadStudyParam.ts";

export type StyleTypeOptionsMap = {
    line: {
        lineColor: string,
        lineOpacity: number,
    },
    marker: object,
    highlight: {
        highlightColor: string,
        highlightOpacity: number,
    },
    fill: {
        fillColor: string,
        fillOpacity: number,
    }
}

export type StyleTypeMap = {
    line: {
        type: "line",
        key: StudyParam<"Number">,
        options: StyleTypeOptionsMap["line"]
    },
    marker: {
        type: "marker",
        key: StudyParam<"Boolean">,
        options: object
    },
    highlight: {
        type: "highlight",
        key: StudyParam<"Boolean">,
        options: StyleTypeOptionsMap["highlight"]
    },
    fill: {
        type: "fill",
        line1Key: StudyParam<"Number">,
        line2Key: StudyParam<"Number">,
        options: StyleTypeOptionsMap["fill"]
    }
}

export type StyleType = keyof StyleTypeMap
export type Style = StyleTypeMap[keyof StyleTypeMap] & {name: string}

export type TerminalInput = {
    timeSeriesList: {
        instrumentId: string,
        timeframe: string
    }[],
    expressions: SingleExpression[],
    styles: Style[]
}

export type TCControl = {
    expressions: SingleExpression[],
    styles: Style[],
    sendTerminalMessage: (input: TerminalInput) => void,
    sendEonTerminalMessage: (prompt: string, input: TerminalInput) => void,
}

export function useTerminalData(brokerId: BrokerId) {

    const studyParserRef = useRef(new ChartElementsParser([]));
    const actionParserRef = useRef(new ChartElementsParser([]));

    const socketUrl = useMemo(() => {
        return `${URL_SOCKET_TERMINAL}/${brokerId}`
    }, [brokerId]);

    const {lastJsonMessage, sendJsonMessage} = useWebSocket<TCMessage>(socketUrl, {disableJson: false});

    const [TCData, setTCData] = useState<TCData & TCControl>({
        expressions: [],
        styles: [],
        sendTerminalMessage(input: TerminalInput) {
            const obj = {
                ...input,
                expressions: input.expressions.map(c => {
                    return {
                        ...c,
                        expression: {
                            type: "Expression",
                            value: c.expression.map(b => {
                                return b.toDb();
                            })
                        }
                    }
                })
            }
            sendJsonMessage({
                type: "Manual",
                input: obj
            })
        },
        sendEonTerminalMessage(prompt: string, input: TerminalInput) {
            const obj = {
                ...input,
                expressions: input.expressions.map(c => {
                    return {
                        ...c,
                        expression: {
                            type: "Expression",
                            value: c.expression.map(b => {
                                // return BlockConverters[b.type].toDb(b, {});
                                return b.toDb();
                            })
                        }
                    }
                })
            }
            sendJsonMessage({
                type: "Eon",
                input: obj,
                prompt: prompt,
            })
        }
    });

    useEffect(() => {
        if (!lastJsonMessage) return;
        console.log(`Received TCMessage: ${JSON.stringify(lastJsonMessage)}`)
        if (checkTCMessageType("config", lastJsonMessage)) {
            const studyHeadersWithStore: ChartElementHeaderWithStore[] = lastJsonMessage.data.studyHeaders.map(h => {
                return {
                    ...h,
                    store: new TimeSeriesStore<ChartElementDataItem[typeof h.type]>(i => i.time as number),
                    optionsStore: new MutableValueStore(h.staticOpts)
                }
            });
            const actionHeadersWithStore: ChartElementHeaderWithStore[] = lastJsonMessage.data.actionHeaders.map(h => {
                return {
                    ...h,
                    store: new TimeSeriesStore<ChartElementDataItem[typeof h.type]>(i => i.time as number),
                    optionsStore: new MutableValueStore(h.staticOpts)
                }
            });

            studyParserRef.current = new ChartElementsParser(studyHeadersWithStore);
            actionParserRef.current = new ChartElementsParser(actionHeadersWithStore);

            setTCData((prevState) => {
                return {
                    ...prevState,
                    strategy: {
                        name: lastJsonMessage.data.name,
                        mode: lastJsonMessage.data.mode
                    },
                    studyElementHeaders: studyHeadersWithStore,
                    actionElementHeaders: actionHeadersWithStore
                }
            })
        } else if (checkTCMessageType("data", lastJsonMessage)) {
            setTCData(prevState => {
                console.log(lastJsonMessage.data)
                return {
                    ...prevState,
                    expressions: lastJsonMessage.data.expressions.map(e => ({
                        ...e,
                        expression: Expression.fromDb(e.expression).value
                    })),
                    styles: lastJsonMessage.data.styles
                }
            })
        } else if (checkTCMessageType("studyUpdates", lastJsonMessage)) {
            studyParserRef.current.parseData(lastJsonMessage.data.elements)
        } else if (checkTCMessageType("actionUpdates", lastJsonMessage)) {
            // actionParserRef.current.parseData(lastJsonMessage.data.elements)
        } else if (checkTCMessageType("studyUpdate", lastJsonMessage)) {
            studyParserRef.current.parseUpdate(lastJsonMessage.data.elements)
            // } else if (checkTCMessageType("actionUpdate", lastJsonMessage)) {
            actionParserRef.current.parseUpdate(lastJsonMessage.data.elements)
        } else if (checkTCMessageType("tradeUpdate", lastJsonMessage)) {
            // setTCData(prevState => {
            //     return {
            //         ...prevState,
            //         trades: {
            //             ...(prevState.trades ?? {}),
            //             [lastJsonMessage.data.trade.tradeInstanceId]: lastJsonMessage.data.trade
            //         }
            //     }
            // })
        } else if (checkTCMessageType("pnlUpdate", lastJsonMessage)) {
            // setTCData(prevState => {
            //     return {
            //         ...prevState,
            //         ltps: lastJsonMessage.data.ltp
            //     }
            // })
        } else if (checkTCMessageType("logUpdate", lastJsonMessage)) {
            // setTCData(prevState => {
            //     return {
            //         ...prevState,
            //         logs: [...(prevState.logs ?? []), lastJsonMessage.data.log]
            //     }
            // })
        } else {
            console.error(`Received unknown TCMessage: ${lastJsonMessage}`);
        }
    }, [lastJsonMessage]);

    return TCData;
}