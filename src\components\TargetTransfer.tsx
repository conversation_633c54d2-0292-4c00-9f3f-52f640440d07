import {GetProp, Table, TableColumnsType, TableProps, Transfer, TransferProps} from "antd";
import React, {useState} from "react";
import {Target} from "../types/strategy/Target.ts";
import {TargetTableColumns} from "../ui/strategy/Targets.tsx";



type TransferItem = GetProp<TransferProps<Target>, 'dataSource'>[number];
type TableRowSelection<T extends object> = TableProps<T>['rowSelection'];

interface TableTransferProps extends TransferProps<TransferItem> {
    dataSource: Target[];
    leftColumns: TableColumnsType<Target>;
    rightColumns: TableColumnsType<Target>;
}

const TableTransfer: React.FC<TableTransferProps> = (props) => {
    const { leftColumns, rightColumns, ...restProps } = props;
    return (
        <Transfer style={{ width: '100%' }} {...restProps}>
            {({
                  direction,
                  filteredItems,
                  onItemSelect,
                  onItemSelectAll,
                  selectedKeys: listSelectedKeys,
                  disabled: listDisabled,
              }) => {
                const columns = direction === 'left' ? leftColumns : rightColumns;
                const rowSelection: TableRowSelection<TransferItem> = {
                    getCheckboxProps: () => ({ disabled: listDisabled }),
                    onChange(selectedRowKeys) {
                        onItemSelectAll(selectedRowKeys, 'replace');
                    },
                    selectedRowKeys: listSelectedKeys,
                    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
                };

                return (
                    <Table<Target>
                        pagination={false}
                        rowSelection={rowSelection}
                        columns={columns}
                        dataSource={filteredItems}
                        size="small"
                        style={{ pointerEvents: listDisabled ? 'none' : undefined }}
                        onRow={(dt) => ({
                            onClick: () => {
                                // @ts-expect-error itemDisabled will be set by the antd component api
                                if (dt.itemDisabled || listDisabled) {
                                    return;
                                }
                                // @ts-expect-error key will be set by the antd component api
                                onItemSelect(dt.key, !listSelectedKeys.includes(dt.key));
                            },
                        })}
                    />
                );
            }}
        </Transfer>
    );
};

// const filterOption = (input: string, item: Target) =>
//     item.title?.includes(input) || item.tag?.includes(input);

type TargetTransferProps = {
    allTargets: Target[],
    onTargetsChanged: (selectedTargets: Target[]) => void
}
export function TargetTransfer(props: TargetTransferProps) {
    const [targetKeys, setTargetKeys] = useState<TransferProps['targetKeys']>([]);

    const onChange: TableTransferProps['onChange'] = (nextTargetKeys) => {
        setTargetKeys(nextTargetKeys);
        const newTargets = nextTargetKeys.map(k => props.allTargets.find(t => t.id === k)).filter(x => x !== undefined);
        props.onTargetsChanged(newTargets)
    };

    return (
            <TableTransfer
                rowKey={(dt) => dt.id}
                dataSource={props.allTargets}
                targetKeys={targetKeys}
                showSelectAll={false}
                onChange={onChange}
                leftColumns={TargetTableColumns!}
                rightColumns={TargetTableColumns!}
            />
    );
};