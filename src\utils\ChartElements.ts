import {<PERSON><PERSON><PERSON>, Ohlc<PERSON>ata, Series<PERSON><PERSON><PERSON>, UTCTimestamp} from "lightweight-charts";
import * as _ from "lodash";

export type ChartElementTypeMap = {
    ohlc: OhlcData[],
    line: LineData[],
    marker: SeriesMarker<UTCTimestamp>[],
    multiline: Record<string, LineData[]>,
    fill: { value1: number, value2: number, time: UTCTimestamp }[]
}


export type ChartElementType = keyof ChartElementTypeMap;

export type ChartElementsData = {
    [key in ChartElementType]: Record<string, ChartElementTypeMap[key]>
}
export type ChartElements = {
    [key in ChartElementType]: Record<string, {
        isVisible?: boolean,
        data: ChartElementTypeMap[key],
        group?: string,
        color?: string
    }>
}

export function emptyChartElements(): ChartElements{
    return {
        line: {}, marker: {}, multiline: {}, ohlc: {}, fill: {}
    }
}

// TODO: In future maybe, can be an object or an array
// Also a special string that works as a placeholder
type BlueprintValue = string | number | boolean

type CommonChartElementOptions = {
    isVisible?: boolean,
    group?: string
}

type ChartElementOptions = {
    line: {},
    marker: {},
    multiline: {},
    fill: {
        // hex color
        fillColor: string,
        // number between 0 and 1 (both inclusive)
        fillOpacity: number
    },
    ohlc: { }
}

export type ChartElementHeaderX<T extends ChartElementType = ChartElementType> = {
    type: T,
    name: string,
    blueprint: { [key in keyof ChartElementTypeMap[T]]: BlueprintValue },
    staticOpts:  ChartElementOptions[T] & CommonChartElementOptions
}
type Row = [number, ...(number | string | boolean | Row)[]]
export type ChartElementsRow = Row

export function checkHeaderType<T extends ChartElementType>(type: T, header: ChartElementHeaderX): header is ChartElementHeaderX<T> {
    return type === header.type
}

// const regex = /^%(\d+)%$/; // Regex to match the format %index%
export class ChartElementsParserX {

    headers: ChartElementHeaderX<ChartElementType>[] = []

    constructor(headers: ChartElementHeaderX<ChartElementType>[]) {
        this.setHeaders(headers)
    }

    setHeaders(headers: ChartElementHeaderX<ChartElementType>[]) {
        this.headers = headers;
    }

    parse(rows: Row[]): ChartElements {
        const parsed = this.parseRows(rows)
        // @ts-expect-error todo: fix
        return _.mapValues(parsed, (v) => {
            return _.mapValues(v, (value, key) => {
                const options = this.headers.find(h => h.name === key)?.staticOpts
                return {
                    ...options,
                    data: value
                }
            })
        })
    }

    parseWithoutTime(rows: Row[]): ChartElements {
        const parsed = this.parseRowsWithoutTime(rows)
        // @ts-expect-error todo: fix
        return _.mapValues(parsed, (v) => {
            return _.mapValues(v, (value, key) => {
                const options = this.headers.find(h => h.name === key)?.staticOpts
                return {
                    ...options,
                    data: value
                }
            })
        })
    }

    parseRows(rows: Row[]): ChartElementsData {
        const chartElements: ChartElementsData = {
            ohlc: {},
            line: {},
            marker: {},
            multiline: {},
            fill: {}
        }
        rows.forEach(row => {
            const [time, ...rest] = row
            this.headers.forEach((header, headerIdx) => {
                const currentItem = rest[headerIdx]
                // TODO: think what to do for null values
                if (header.type === "marker" && currentItem === null) return;
                if (header.type === "multiline"){
                    // currentitem will be []
                    let prev = chartElements[header.type][header.name] ?? {}
                    if (_.isArray(currentItem)){
                        currentItem.forEach(item => {
                            if (_.isArray(item)){
                                const [id, ...rest] = item
                                // @ts-expect-error todo
                                const data = buildBlueprint(header, time, rest)
                                if (data === undefined) return
                                prev = {
                                    ...prev,
                                    [id]: [...(prev[id] ?? []), data]
                                }
                            } else throw new Error("should be always array")
                        })
                    } else throw new Error("should be always array")
                    chartElements[header.type] = {
                        ...chartElements[header.type],
                        [header.name]: prev
                    }
                    return;
                }

                const data = buildBlueprint(header, time, currentItem)

                // const data = _.mapValues(header.blueprint, (value, key) => {
                //     // if is %time% -> replace it with time
                //     if (value === "%time%") return time
                //     else if (typeof value === "string") {
                //         // TODO: Use regex and replace
                //         if (value.startsWith("%") && value.endsWith("%")) {
                //             const index = parseInt(value.replace("%", ""))
                //             if (_.isArray(currentItem)){
                //                 const out = currentItem[index]
                //                 if (out === undefined) throw new Error(`Expected value for ${key}. Header: ${header}`)
                //                 return out
                //             } else {
                //                 if (index === 0)
                //                     return currentItem
                //                 else throw new Error(`Expected array value at index ${index} for ${key}. Header: ${header}`)
                //             }
                //         } else return value
                //     } else return value
                // })
                if (data === undefined) return
                // @ts-expect-error TODO write a message
                chartElements[header.type] = {
                    ...chartElements[header.type],
                    [header.name]: [...(chartElements[header.type][header.name] ?? []), data]
                }
            })
        })
        return chartElements
    }

    parseRowsWithoutTime(rows: Row[]): ChartElementsData {
        const chartElements: ChartElementsData = {
            ohlc: {},
            line: {},
            marker: {},
            multiline: {},
            fill: {}
        }
        rows.forEach(row => {
            const rest = row
            this.headers.forEach((header, headerIdx) => {
                const currentItem = rest[headerIdx]
                // TODO: think what to do for null values
                if (header.type === "marker" && currentItem === null) return;
                if (header.type === "multiline"){
                    // currentitem will be []
                    let prev = chartElements[header.type][header.name] ?? {}
                    if (_.isArray(currentItem)){
                        currentItem.forEach(item => {
                            if (_.isArray(item)){
                                const [id, ...rest] = item
                                // @ts-expect-error todo
                                const data = buildBlueprint(header, time, rest)
                                if (data === undefined) return
                                prev = {
                                    ...prev,
                                    [id]: [...(prev[id] ?? []), data]
                                }
                            } else throw new Error("should be always array")
                        })
                    } else throw new Error("should be always array")
                    chartElements[header.type] = {
                        ...chartElements[header.type],
                        [header.name]: prev
                    }
                    return;
                }

                const data = buildBlueprint(header, NaN, currentItem)
                if (data === undefined) return
                // @ts-expect-error TODO write a message
                chartElements[header.type] = {
                    ...chartElements[header.type],
                    [header.name]: [...(chartElements[header.type][header.name] ?? []), data]
                }
            })
        })
        return chartElements
    }
}

function buildBlueprint(header: ChartElementHeaderX<keyof ChartElementTypeMap>, time: number, currentItem: string | number | boolean | Row){
    return _.mapValues(header.blueprint, (value, key) => {
        // if is %time% -> replace it with time
        if (value === "%time%") return time
        else if (typeof value === "string") {
            // TODO: Use regex and replace
            if (value.startsWith("%") && value.endsWith("%")) {
                const index = parseInt(value.replace("%", ""))
                if (_.isArray(currentItem)) {
                    const out = currentItem[index]
                    if (out === undefined) throw new Error(`Expected value for ${key}. Header: ${header}`)
                    return out
                } else {
                    if (index === 0)
                        return currentItem
                    else throw new Error(`Expected array value at index ${index} for ${key}. Header: ${header}`)
                }
            } else return value
        } else return value
    })
}

export const temp = {}