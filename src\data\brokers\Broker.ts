
const AllBrokers = {
    Fyers: {
        id: "fyers",
        name: "<PERSON><PERSON>"
    },
    Delta: {
        id: "delta",
        name: "Delta Exchange"
    }
} as const;
export type Broker = (typeof AllBrokers)[keyof typeof AllBrokers];
export type BrokerId = Broker["id"]
const AllBrokersList: Broker[] = Object.values(AllBrokers);

export const Brokers = {
    ...AllBrokers,
    all: AllBrokersList,
    getById: (id: string) => {
        const broker = AllBrokersList.find((b) => b.id === id);
        if (!broker) {
            throw new Error(`Broker with id "${id}" not found.`);
        }
        return broker;
    }
}


