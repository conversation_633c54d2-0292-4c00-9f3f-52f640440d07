import { Tooltip } from "antd";
import React from "react";

interface ToggleButtonProps {
    id: string;
    label: string;
    icon: React.ReactNode;
    iconToggled: React.ReactNode;
    isActive: boolean;
    onToggle: (id: string) => void;
}

export const IconToggle: React.FC<ToggleButtonProps> = ({
                                                            id,
                                                            label,
                                                            icon,
                                                            iconToggled,
                                                            isActive,
                                                            onToggle
                                                        }) => {
    return (
        <Tooltip title={label} placement="left">
            <button
                onClick={() => onToggle(id)}
                className={`
                    flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200 ease-in-out
                    ${isActive
                    ? 'border-[#7e92f4] border-2'
                    : 'text-gray-600 hover:border-gray-300 hover:bg-gray-200'}
                `}
                aria-pressed={isActive}
                aria-label={`${isActive ? 'Deactivate' : 'Activate'} ${label}`}
            >
                <span className={`w-5 h-5 ${isActive ? 'text-[#7e92f4]' : 'text-gray-500'}`}>
                    {isActive ? iconToggled : icon}
                </span>
            </button>
        </Tooltip>
    );
};
