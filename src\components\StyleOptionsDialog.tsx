import * as Popover from '@radix-ui/react-popover';
import * as Slider from '@radix-ui/react-slider';
import { ReactNode, useState } from 'react';
import { Cross2Icon } from '@radix-ui/react-icons';
import ColorPicker from './ColorPicker.tsx';
import { Style } from '../data/Terminal.ts';

interface StyleOptionsPopoverProps {
  initialStyle: Style;
  onSave: (style: Style) => void;
  children: ReactNode;
}

const StyleOptionsPopover: React.FC<StyleOptionsPopoverProps> = ({
                                                                   initialStyle,
                                                                   onSave,
                                                                   children,
                                                                 }) => {
  // Initialize internal state with initialStyle
  const [localStyle, setLocalStyle] = useState<Style>(initialStyle);
  const [open, setOpen] = useState(false);

  // Render color and opacity controls
  const renderColorAndOpacity = (
      color: string,
      opacity: number,
      setColor: (color: string) => void,
      setOpacity: (opacity: number) => void,
      label: string,
  ) => (
      <div className="space-y-4 w-full">
        <div className="flex items-center space-x-8">
          <label className="text-sm font-medium text-gray-700" htmlFor={`${label}-color`}>
            {`${label} Color`}
          </label>
          <ColorPicker color={color} setColor={setColor} />
        </div>
        <div className="flex items-center space-x-8 w-full">
          <label className="text-sm font-medium text-gray-700" htmlFor={`${label}-opacity`}>
            {`${label} Opacity`}
          </label>
          <div className="relative flex-1">
            <Slider.Root
                className="flex items-center h-6 w-full touch-none"
                value={[opacity]}
                onValueChange={([value]) => setOpacity(value)}
                min={0}
                max={1}
                step={0.01}
                aria-label={`${label} Opacity Slider`}
            >
              <Slider.Track className="bg-gray-200 relative flex-1 h-1.5 rounded-full">
                <Slider.Range className="absolute bg-indigo-500 h-1.5 rounded-full transition-all" />
              </Slider.Track>
              <Slider.Thumb
                  className="block w-3 h-3 bg-indigo-500 rounded-full shadow-md hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-400 transition-all"
                  aria-label={`${label} Opacity Thumb`}
              />
            </Slider.Root>
          </div>
          <span className="text-sm text-gray-600 block">{opacity.toFixed(2)}</span>
        </div>
      </div>
  );

  // Render controls based on style type
  const renderStyleControls = () => {
    switch (localStyle.type) {
      case 'line':
        return renderColorAndOpacity(
            localStyle.options.lineColor,
            localStyle.options.lineOpacity,
            (color) => setLocalStyle({ ...localStyle, options: { ...localStyle.options, lineColor: color } }),
            (opacity) => setLocalStyle({ ...localStyle, options: { ...localStyle.options, lineOpacity: opacity } }),
            'Line',
        );
      case 'highlight':
        return renderColorAndOpacity(
            localStyle.options.highlightColor,
            localStyle.options.highlightOpacity,
            (color) => setLocalStyle({ ...localStyle, options: { ...localStyle.options, highlightColor: color } }),
            (opacity) => setLocalStyle({ ...localStyle, options: { ...localStyle.options, highlightOpacity: opacity } }),
            'Highlight',
        );
      case 'fill':
        return renderColorAndOpacity(
            localStyle.options.fillColor,
            localStyle.options.fillOpacity,
            (color) => setLocalStyle({ ...localStyle, options: { ...localStyle.options, fillColor: color } }),
            (opacity) => setLocalStyle({ ...localStyle, options: { ...localStyle.options, fillOpacity: opacity } }),
            'Fill',
        );
      case 'marker':
        return (
            <div className="text-sm text-gray-600 italic">
              No configurable options for marker style.
            </div>
        );
      default:
        return null;
    }
  };

  return (
      <Popover.Root open={open} onOpenChange={setOpen}>
        <Popover.Trigger asChild>{children}</Popover.Trigger>
        <Popover.Portal>
          <Popover.Content
              className="bg-white p-6 rounded-xl shadow-2xl w-80 border border-gray-100 animate-in fade-in-0 zoom-in-95 duration-200"
              sideOffset={8}
              align="start"
              tabIndex={-1}
              aria-labelledby="style-options-title"
          >
            <h2 id="style-options-title" className="text-lg font-semibold text-gray-800 mb-5">
              {localStyle.type.charAt(0).toUpperCase() + localStyle.type.slice(1)} Style Options
            </h2>

            <div className="mb-6">{renderStyleControls()}</div>

            <div className="flex justify-end">
              <button
                  onClick={() => {
                    onSave(localStyle);
                    setOpen(false);
                  }}
                  className="px-4 py-2 text-sm font-medium text-white bg-indigo-500 rounded-lg hover:bg-indigo-600 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                  aria-label="Save style changes"
              >
                Save
              </button>
            </div>
            <Popover.Close asChild>
              <button
                  className="absolute top-3 right-3 p-1.5 text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-400 rounded-full transition-colors duration-150"
                  aria-label="Close popover"
              >
                <Cross2Icon className="w-5 h-5" />
              </button>
            </Popover.Close>
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
  );
};

export default StyleOptionsPopover;