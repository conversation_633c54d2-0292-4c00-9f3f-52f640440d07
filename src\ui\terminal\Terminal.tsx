import {<PERSON><PERSON><PERSON>} from "../../chart/TVChart.tsx";
import useContainerSize from "../../utils/hooks/useContainerSize.ts";
import React, {useMemo, useRef, useState} from "react";
import {Badge, Button, Segmented, Tag, Tooltip} from "antd";
import {AppstoreOutlined, Bar<PERSON><PERSON>Outlined, PauseOutlined} from "@ant-design/icons";
import {LiveConnectionDot} from "../../components/LiveConnectionDot.tsx";
import {
    ArrowsUpDownIcon as ArrowsUpDownIconOutlined,
    ChartPieIcon as ChartPieIconOutlined,
    CommandLineIcon as CommandLineIconOutlined,
    EyeIcon,
    EyeSlashIcon
} from "@heroicons/react/24/outline";
import {IconToggle} from "../../components/IconToggle.tsx";
import {ClipboardDocumentListIcon as ClipboardDocumentListIconOutlined} from "@heroicons/react/24/outline";
import {Cog6ToothIcon, CommandLineIcon} from "@heroicons/react/24/solid";
import {ArrowsUpDownIcon, ChartPieIcon, ClipboardDocumentListIcon} from "@heroicons/react/24/solid";
import {StatChip} from "../../components/StatChip.tsx";
import {StatsView} from "./StatsView.tsx";
import {TCData} from "../../data/StrategyTerminal.ts";
import _ from "lodash";
import {calculateTotalPnL} from "../../data/Pnl.ts";
import {LogViewer} from "../../components/Logs.tsx";
import {TradeComp} from "./TradeComp.tsx";
import {
    ChartElementHeaderWithStore,
    checkHeaderWithStoreType
} from "../../utils/ChartElementsParser.ts";
import {MorphedTimeSeriesStore} from "../../data/execution/MorphedTimeSeriesStore.ts";
import {useOhlcData} from "../../data/execution/candles.ts";

// actions -> Stop strategy, Pause Strategy

/*
 * TODO UI: Collapsible Tabs
 * */
export function Terminal(props: { TCData: TCData }) {

    // const { id } = useParams<{ id: string }>();
    // const {strategy, trades, ltps, logs, studyElementStores, actionElementStores, clientHandle} = useTerminalConnectionNew(id);
    const {strategy, trades, ltps, logs, studyElementHeaders, actionElementHeaders, clientHandle} = props.TCData;

    // const allStores = useMemo(() => {
    //     if (studyElementHeaders && actionElementHeaders)
    //         return  [...studyElementHeaders, ...actionElementHeaders]
    //     else return []
    // }, [studyElementHeaders, actionElementHeaders]);

    const timeSeries: { token: string, timeframe: string, intervalSeconds: number } | undefined = useMemo(() => {
        return undefined
        return {
            // token: "NSE:NIFTY2561924850PE",
            token: "NSE:NIFTY2571025500CE",
            timeframe: "15S",
            intervalSeconds: 15,
        }
    }, []);

    // const test = useOhlcData(timeSeries);
    // useEffect(() => {
    //     test?.subscribe("s", (x) => {
    //         console.log("x", x)
    //     })
    //     return () => {
    //         test?.unsubscribe("s")
    //     }
    // }, [test]);

    const ohlcStore = useOhlcData(timeSeries);

    const s = useMemo(() => {
        return ohlcStore?.store ?? studyElementHeaders?.find(s => checkHeaderWithStoreType("ohlc", s))?.store;
    }, [ohlcStore, studyElementHeaders]);

    // const finalOhlcStore: DataStore<ChartElementDataItem["ohlc"]> | undefined = useMemo(() => {
    //     return
    // }, [ohlcStore, studyElementHeaders]);

    // TODO: Should be async
    // TODO: Take timeseries input
    // TODO: Morph only those stores which have different timeseries, timeseries should be provided in the headers
    const headers: ChartElementHeaderWithStore[] = useMemo(() => {
        const intervalSeconds = timeSeries?.intervalSeconds ?? 20; // TODO

        const finalOhlcStore = ohlcStore?.store ?? studyElementHeaders?.find(s => checkHeaderWithStoreType("ohlc", s))?.store;

        if (finalOhlcStore === undefined) return [];
        const allHeaders: ChartElementHeaderWithStore[] = [];
        if (actionElementHeaders !== undefined) allHeaders.push(...actionElementHeaders);
        if (studyElementHeaders !== undefined) allHeaders.push(...studyElementHeaders);

        const restHeaders = allHeaders.map(header => {
            return {
                ...header,
                store: MorphedTimeSeriesStore(intervalSeconds, finalOhlcStore, header.store)
            }
        });

        return [...restHeaders];
    }, [timeSeries, ohlcStore, studyElementHeaders, actionElementHeaders]);

    const [view, setView] = useState<"chart" | "stats">("chart");

    const [activeTabs, setActiveTabs] = useState<Set<"trades" | "positions" | "orders" | "logs">>(new Set(["trades"]))
    const [selectedTradeType, setSelectedTradeType] = useState<"Active" | "Completed">("Active");
    const {ref, size} = useContainerSize<HTMLDivElement>();

    // const [lastVisitedLogTime, setLastVisitedLogTime] = useState<number | undefined>(undefined)
    const lastVisitedLogTime = useRef<number | undefined>();
    const unvisitedLogs = useMemo(() => {
        if (!logs || activeTabs.has("logs")) return 0;

        return logs.filter(log => log.time > (lastVisitedLogTime.current ?? 0)).length
    }, [activeTabs, logs]);


    const activeTrades = useMemo(() => {
        if (!trades) return []
        return Object.values(trades).filter((trade) => trade.isActive);
    }, [trades]);
    const completedTrades = useMemo(() => {
        if (!trades) return []
        return Object.values(trades).filter((trade) => !trade.isActive);
    }, [trades]);

    const tradesPnl = useMemo(() => {
        if (!trades || !ltps) return {};
        return _.mapValues(trades, (trade) => {
            const positions = trade.actions.flatMap(action => action.positions)
            return calculateTotalPnL(positions, ltps);
        })
    }, [trades, ltps]);

    const overallPnl = useMemo(() => {
        return _.sum(Object.values(tradesPnl)).toFixed(2)
    }, [tradesPnl]);

    const chartKey = useMemo(() => {
        return "ccc"
        // return `container${_.join([...activeTabs])}`
    }, []);

    const [indicatorsTab, setIndicatorsTab] = useState(false)

    return (
        <div className={"h-screen max-h-screen flex overflow-y-hidden"}>
            {/*  Sidebar  */}
            <div className="w-0 bg-[#fafafc] border-r-2">
            </div>

            <div className={"flex-1 min-h-0 flex flex-col w-full max-h-screen"}>
                {/* Header */}
                <div className="h-14 bg-[#fafafc] border-b-2">
                    <div className={"w-full h-full px-4 py-2 flex items-center space-x-2"}>
                        <h1 className={"text-zinc-700 font-semibold text-lg"}>{strategy?.name}</h1>
                        <Tag color={"gold"} className={"mt-1 ml-4 text-md"}>{_.capitalize(strategy?.mode)}</Tag>
                        <div className="flex-1 min-h-0"></div>
                        {
                            (strategy?.mode === "restricted" || strategy?.mode === "unrestricted") &&
                            <>
                                <LiveConnectionDot size="sm" className={""} isConnected={true}/>
                                <Button color={"blue"} variant={"outlined"} icon={<PauseOutlined/>}
                                        iconPosition={"end"}>Pause</Button>
                                <Button color={"danger"} variant={"solid"}
                                        onClick={() => clientHandle?.stopStrategy()}>Stop</Button>
                            </>
                        }
                    </div>
                </div>
                <div className={`bg-[#f3f4fb] flex-1 min-h-0 flex`}>
                    {/* Chart Container */}
                    <div className={"flex-[1] border-r-2 border-b-2 flex flex-col"}>
                        <div className=" bg-[#fafafc] border-b-2">
                            <div className={"w-full h-full px-3 py-2 flex items-center space-x-2"}>
                                <Segmented<typeof view>
                                    value={view}
                                    onChange={setView}
                                    options={[
                                        {
                                            value: 'chart',
                                            icon: <Tooltip title={"Chart View"}><BarChartOutlined/></Tooltip>
                                        },
                                        {
                                            value: 'stats',
                                            icon: <Tooltip title={"Stats View"}><AppstoreOutlined/></Tooltip>
                                        },
                                    ]}
                                />
                                <StatChip label={"Overall Pnl"}
                                          value={<span className={"text-emerald-600"}>{overallPnl}</span>}/>
                                <StatChip label={"Closed Trades"} value={completedTrades.length}/>
                                <StatChip label={"Active Trades"} value={activeTrades.length}/>
                            </div>
                        </div>
                        <div className={"flex-1  min-h-0 flex"}>
                            <div className={"flex-1 min-h-0"} style={{display: view === "chart" ? "block" : "none"}}>
                                <div className={"flex h-full "}>
                                    <div className="w-12 bg-[#fafafc] border-r-2 items-center flex flex-col py-2">
                                        <IconToggle
                                            key={"indicators"}
                                            id={"indicators"}
                                            label={"Indicators"}
                                            // @ts-expect-error solve later
                                            icon={ClipboardDocumentListIconOutlined}
                                            // @ts-expect-error solve later
                                            iconToggled={ClipboardDocumentListIcon}
                                            isActive={indicatorsTab}
                                            onToggle={() => {
                                                setIndicatorsTab(!indicatorsTab);
                                            }}
                                        />
                                    </div>
                                    <div className={`w-60 bg-[#fafafc] border-r-2`}
                                         style={{display: indicatorsTab ? "block" : "none", overflowY: "auto"}}>
                                        {
                                            headers.map(h => (
                                                <IndicatorItem
                                                    key={h.name}
                                                    name={h.name}
                                                    color={h.staticOpts["lineColor"]}
                                                    isVisible={h.optionsStore.value().isVisible}
                                                    onToggleVisibility={(i) => {
                                                        h.optionsStore.updateFn(p => ({...p, isVisible: i}));
                                                    }}/>
                                            ))
                                        }
                                    </div>
                                    <div className="flex-1 relative" ref={ref}>
                                        <TVChart ohlcStore={s} key={chartKey} stores={headers} containerKey={"main"}
                                                 width={size.width}
                                                 height={size.height}/>
                                    </div>
                                </div>

                            </div>
                            {
                                view === "stats" &&
                                <StatsView/>
                            }
                        </div>
                    </div>


                    {
                        // Logs Container
                        activeTabs.has("logs") &&

                        <div className={"bg-[#fafafc] w-60 flex flex-col min-h-0 border-r-2"}>
                            <div className={"pl-3 pr-1 py-1 border-b flex items-center"}>
                                <h1 className={"text-zinc-700 font-semibold "}>Logs</h1>
                                <div className="flex-1 min-h-0"></div>
                                <div className={"p-2 text-zinc-700 cursor-pointer rounded-full hover:bg-zinc-700/20"}>
                                    <Cog6ToothIcon className={"w-4 h-4"}/>
                                </div>
                            </div>
                            <div className={"flex-1 min-h-0 overflow-y-scroll"}>
                                <LogViewer logs={logs ?? []} theme={"light"} lastVisited={l => {
                                    lastVisitedLogTime.current = l.time
                                }}/>
                            </div>

                        </div>
                    }

                    {
                        // Logs Container
                        (activeTabs.has("positions") || activeTabs.has("orders")) &&

                        <div className={"bg-[#fafafc] w-60 flex flex-col min-h-0 border-r-2"}>
                            {
                                activeTabs.has("positions") &&
                                <>
                                    <div className={"pl-3 pr-1 py-1 border-b flex items-center"}>
                                        <h1 className={"text-zinc-700 font-semibold "}>Positions</h1>
                                        <div className="flex-1 min-h-0"></div>
                                        <div
                                            className={"p-2 text-zinc-700 cursor-pointer rounded-full hover:bg-zinc-700/20"}>
                                            <Cog6ToothIcon className={"w-4 h-4"}/>
                                        </div>
                                    </div>
                                    <div className={"flex-1 min-h-0 overflow-y-scroll"}>
                                    </div>
                                </>
                            }
                            {
                                activeTabs.has("orders") &&
                                <>
                                    <div className={"pl-3 pr-1 py-1 border-y flex items-center"}>
                                        <h1 className={"text-zinc-700 font-semibold "}>Orders</h1>
                                        <div className="flex-1 min-h-0"></div>
                                        <div
                                            className={"p-2 text-zinc-700 cursor-pointer rounded-full hover:bg-zinc-700/20"}>
                                            <Cog6ToothIcon className={"w-4 h-4"}/>
                                        </div>
                                    </div>
                                    <div className={"flex-1 min-h-0 overflow-y-scroll"}>
                                    </div>
                                </>
                            }
                        </div>
                    }

                    {/* Strategy/Trades Container */}
                    {
                        activeTabs.has("trades") &&

                        <div className={"bg-[#fafafc] w-60 flex flex-col min-h-0"}>
                            <div className={"pl-3 pr-1 py-1 border-b flex items-center"}>
                                <h1 className={"text-zinc-700 font-semibold "}>Trades</h1>
                                <div className="flex-1 min-h-0"></div>
                                <div className={"p-2 text-zinc-700 cursor-pointer rounded-full hover:bg-zinc-700/20"}>
                                    <Cog6ToothIcon className={"w-4 h-4"}/>
                                </div>
                            </div>
                            <div className={"px-3 py-2 border-b"}>
                                <Segmented<typeof selectedTradeType>
                                    value={selectedTradeType}
                                    options={['Active', 'Completed']}
                                    onChange={setSelectedTradeType}
                                />
                            </div>
                            <div className={"flex-1 p-2 min-h-0 overflow-y-auto"}>
                                <div className={""}>
                                    {
                                        (selectedTradeType === "Active" ? activeTrades : completedTrades).map(trade => (
                                            <TradeComp trade={trade} key={trade.tradeInstanceId}
                                                       allowActions={selectedTradeType === "Active"}
                                                       onClose={() => clientHandle?.closeTrade(trade.tradeInstanceId)}
                                                       pnl={tradesPnl[trade.tradeInstanceId]}/>
                                        ))
                                    }
                                </div>
                            </div>

                        </div>
                    }

                    {/* Right Side Bar */}
                    <div className={"w-12 bg-[#fafafc] border-l-2"}>
                        <TabsToggleList activeTabs={activeTabs} setActiveTabs={setActiveTabs}
                                        counts={{logs: unvisitedLogs}}/>
                    </div>
                </div>
            </div>
        </div>
    )
        ;
}

type IconToggleListProps = {
    activeTabs: Set<"trades" | "positions" | "orders" | "logs">,
    setActiveTabs: (activeTabs: Set<"trades" | "positions" | "orders" | "logs">) => void,
    counts: Partial<Record<"trades" | "positions" | "orders" | "logs", number>>
}

const TabsToggleList: React.FC<IconToggleListProps> = ({activeTabs, setActiveTabs, counts}: IconToggleListProps) => {
    const toggleButtons = [
        {id: 'trades', label: 'Trades', icon: ArrowsUpDownIconOutlined, iconToggled: ArrowsUpDownIcon},
        {id: 'positions', label: 'Positions', icon: ChartPieIconOutlined, iconToggled: ChartPieIcon},
        {
            id: 'orders',
            label: 'Orders',
            icon: ClipboardDocumentListIconOutlined,
            iconToggled: ClipboardDocumentListIcon
        },
        {id: 'logs', label: 'Logs', icon: CommandLineIconOutlined, iconToggled: CommandLineIcon}
    ];

    const handleToggle = (id: "trades" | "positions" | "orders" | "logs") => {
        const newSet = new Set(activeTabs);
        if (newSet.has(id)) {
            newSet.delete(id);
        } else {
            newSet.add(id);
        }
        setActiveTabs(newSet);
    };

    return (
        <div className="max-w-xs mx-auto p-4">
            <div className="flex flex-col items-center space-y-3">
                {toggleButtons.map((button) => (
                    // @ts-expect-error later
                    <Badge offset={[-6]} size={"small"} count={counts[button.id]}>
                        <IconToggle
                            key={button.id}
                            id={button.id}
                            label={button.label}
                            // @ts-expect-error solve later
                            icon={button.icon}
                            // @ts-expect-error solve later
                            iconToggled={button.iconToggled}
                            isActive={activeTabs.has(button.id as "trades" | "positions" | "orders" | "logs")}
                            // @ts-expect-error solve later
                            onToggle={handleToggle}
                        />
                    </Badge>
                ))}
            </div>
        </div>
    );
};

interface IndicatorItemProps {
    name: string;
    isVisible?: boolean;
    onToggleVisibility: (isVisible: boolean) => void;
    color?: string
}

const IndicatorItem: React.FC<IndicatorItemProps> = ({
                                                         name,
                                                         isVisible: initialVisibility,
                                                         onToggleVisibility,
    color
                                                     }) => {

    const [visibility, setVisibility] = useState(initialVisibility === undefined ? false : initialVisibility);

    return (
        <div
            className="flex items-center justify-between px-3   border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
      <span className="text-gray-800 font-medium truncate flex-1 mr-3">
        {name}
      </span>

            {
                color &&
                <div className={`rounded size-4 mr-1 `} style={{ backgroundColor: color }}>

                </div>
            }

            <button
                onClick={() => {
                    setVisibility(!visibility);
                    onToggleVisibility(!visibility)
                }}
                className={`m-1 p-1 rounded-md transition-colors ${
                    visibility
                        ? 'text-blue-600  hover:bg-gray-100'
                        : 'text-gray-400  hover:bg-gray-100'
                }`}
                aria-label={visibility ? 'Hide indicator' : 'Show indicator'}
            >
                {visibility ? (
                    <EyeIcon className="h-5 w-5"/>
                ) : (
                    <EyeSlashIcon className="h-5 w-5"/>
                )}
            </button>
        </div>
    );
};
