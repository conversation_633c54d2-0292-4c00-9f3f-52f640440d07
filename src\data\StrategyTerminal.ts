// terminal connection messages
// => Config type -> Strategy name, run mode, chart element headers...
// => Strategy running state updates -> Running, Paused, Stopped
// => Chart elements update - all elements are individual elements, token is provided in the headers, morph it to use it on different timeSeries
// => Trade/Action updates - positions and action specific data
// => Pnl updates

import {useEffect, useMemo, useRef, useState} from "react";
import useWebSocket, {ReadyState} from "react-use-websocket";
import {URL_SOCKET_TERMINAL} from "../constants.ts";
import {TimeSeriesStore} from "../utils/Store.ts";
import {LogEntry} from "../components/Logs.tsx";
import {TradeData} from "./Types.ts";
import {
    ChartElementDataItem,
    ChartElementHeader,
    ChartElementHeaderWithStore,
    ChartElementsParser,
    RawChartElement
} from "../utils/ChartElementsParser.ts";
import {MutableValueStore} from "../utils/ValueStore.ts";
import {StudyType} from "../types/study/StudyType.ts";
import {DbBlock} from "../types/study/db/DbBlock.ts";
import {Style} from "./Terminal.ts";

type TCMessageTypeMap = {
    config: {
        name: string,
        mode: "restricted" | "unrestricted" | "backtest",
        studyHeaders: ChartElementHeader[],
        actionHeaders: ChartElementHeader[]
    },
    data: {
        expressions: {
            name: string,
            valueType: StudyType,
            expression: DbBlock<"Expression">
        }[],
        styles: Style[]
    },
    state: "running" | "paused" | "stopped", // TODO: Include pause data if in paused state
    studyUpdate: {
        elements: RawChartElement[],
    },
    studyUpdates: {
        elements: RawChartElement[][],
    },
    actionUpdate: {
        elements: RawChartElement[],
    },
    actionUpdates: {
        elements: RawChartElement[][],
    },
    tradeUpdate: {
        trade: TradeData
    },
    pnlUpdate: {
        ltp: Record<string, number>
    },
    logUpdate: {
        log: LogEntry
    }
}
export type TCMessageType = keyof TCMessageTypeMap

// TODO: Include list or item
export type TCMessage<K extends TCMessageType = TCMessageType> = {
    type: K,
    data: TCMessageTypeMap[K]
}

export function checkTCMessageType<K extends TCMessageType>(type: K, msg: TCMessage): msg is TCMessage<K> {
    return msg.type == type
}
export type TCClientHandle = {
    stopStrategy: () => void;
    closeTrade: (tradeInstanceId: string) => void;
}

export type TCData = Partial<{
    strategy: Pick<TCMessageTypeMap["config"], "name" | "mode">,
    state: TCMessageTypeMap["state"],
    trades: Record<string, TradeData>,
    ltps: Record<string, number>,
    logs: LogEntry[],
    studyElementHeaders: ChartElementHeaderWithStore[],
    actionElementHeaders: ChartElementHeaderWithStore[],
    clientHandle: TCClientHandle
}>

export function useTerminalConnection(id?: string) {

    const studyParserRef = useRef(new ChartElementsParser([]));
    const actionParserRef = useRef(new ChartElementsParser([]));

    const {lastJsonMessage, readyState, sendMessage, sendJsonMessage} = useWebSocket<TCMessage>(URL_SOCKET_TERMINAL, {disableJson: false});

    const clientHandle = useMemo(() => {
        return {
            stopStrategy(){
                sendJsonMessage({
                    type: "stop"
                })
            },
            closeTrade: (tradeInstanceId: string) => {
                sendJsonMessage({
                    type: "closeTrade",
                    tradeInstanceId
                })
            }
        }
    }, [sendJsonMessage]);
    const [TCData, setTCData] = useState<TCData>({ clientHandle });

    useEffect(() => {
        if (readyState == ReadyState.OPEN && id !== undefined){
            console.log("sending")
            sendMessage(id)
        }
    }, [id, readyState, sendMessage]);
    
    useEffect(() => {
        if (!lastJsonMessage) return;
        // console.log(`Received TCMessage: ${JSON.stringify(lastJsonMessage)}`)
        if (checkTCMessageType("config", lastJsonMessage)) {
            const studyHeadersWithStore: ChartElementHeaderWithStore[] = lastJsonMessage.data.studyHeaders.map(h => {
                return {
                    ...h,
                    store: new TimeSeriesStore<ChartElementDataItem[typeof h.type]>(i => i.time as number),
                    optionsStore: new MutableValueStore(h.staticOpts)
                }
            });
            const actionHeadersWithStore: ChartElementHeaderWithStore[] = lastJsonMessage.data.actionHeaders.map(h => {
                return {
                    ...h,
                    store: new TimeSeriesStore<ChartElementDataItem[typeof h.type]>(i => i.time as number),
                    optionsStore: new MutableValueStore(h.staticOpts)
                }
            });

            studyParserRef.current = new ChartElementsParser(studyHeadersWithStore);
            actionParserRef.current = new ChartElementsParser(actionHeadersWithStore);

            setTCData((prevState) => {
                return {
                    ...prevState,
                    strategy: {
                        name: lastJsonMessage.data.name,
                        mode: lastJsonMessage.data.mode
                    },
                    studyElementHeaders: studyHeadersWithStore,
                    actionElementHeaders: actionHeadersWithStore
                }
            })
        }
        else if (checkTCMessageType("studyUpdates", lastJsonMessage)) {
            studyParserRef.current.parseData(lastJsonMessage.data.elements)
        } else if (checkTCMessageType("actionUpdates", lastJsonMessage)) {
            actionParserRef.current.parseData(lastJsonMessage.data.elements)
        } else if (checkTCMessageType("studyUpdate", lastJsonMessage)) {
            studyParserRef.current.parseUpdate(lastJsonMessage.data.elements)
        } else if (checkTCMessageType("actionUpdate", lastJsonMessage)) {
            actionParserRef.current.parseUpdate(lastJsonMessage.data.elements)
        } else if (checkTCMessageType("tradeUpdate", lastJsonMessage)) {
            // console.log("Receiving trade", lastJsonMessage)
            setTCData(prevState => {
                return {
                    ...prevState,
                    trades: {
                        ...(prevState.trades ?? {}),
                        [lastJsonMessage.data.trade.tradeInstanceId]: lastJsonMessage.data.trade
                    }
                }
            })
        }
        else if (checkTCMessageType("pnlUpdate", lastJsonMessage)) {
            setTCData(prevState => {
                return {
                    ...prevState,
                    ltps: lastJsonMessage.data.ltp
                }
            })
        }
        else if (checkTCMessageType("logUpdate", lastJsonMessage)){
            setTCData(prevState => {
                return {
                    ...prevState,
                    logs: [...(prevState.logs ?? []), lastJsonMessage.data.log]
                }
            })
        }
        else {
            console.error(`Received unknown TCMessage: ${lastJsonMessage}`);
        }
    }, [lastJsonMessage]);

    return TCData;
}