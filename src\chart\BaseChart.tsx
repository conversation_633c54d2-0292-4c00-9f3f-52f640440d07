// Base Chart -> Only chart pane, candlestick data, styles

import React, {useCallback, useEffect, useMemo, useRef, useState} from "react";
import {IChartApi, ISeriesApi, LineData, Range, Time, UTCTimestamp} from "lightweight-charts";
import {Chart} from "./lib/Chart.tsx";
import {CandleStickSeries, LineSeries} from "./lib/Series.tsx";
import {DefaultChartOptions} from "./DefaultOptions.ts";
import {getColorFromSeed, getColorFromStringSeed} from "../utils/Color.ts";
import {ChartElements} from "../utils/ChartElements.ts";
import _ from "lodash";

type BaseChartProps = {
    width?: number,
    height?: number,
    mainChartData?: ChartElements["ohlc"],
    lines?: ChartElements["line"],
    markers?: ChartElements["marker"],
    multiline?: ChartElements["multiline"]
    key: React.Key,
    visibleRange?: Range<Time>
}

export function BaseChart(props: BaseChartProps) {

    const topBarRef = useRef<HTMLDivElement>(null);
    const chartRef = useRef<IChartApi>();
    const mainSeriesRef = useRef<ISeriesApi<"Candlestick"> | null>(null);

    // Height updates
    // TODO: Review
    useEffect(() => {
        if (!props.height && !props.width) return
        let obj = {}
        if (props.height) obj = {...obj, height: Math.max(props.height - (topBarRef.current?.clientHeight ?? 0), 0)}
        if (props.width) obj = {...obj, width: props.width}
        chartRef.current?.applyOptions(obj)
    }, [props.height, props.width]);

    const [selected, setSelected] = useState<string>()

    const [range, setRange] = useState<Range<UTCTimestamp> | null>(null)

    // TODO: Also do for lines
    const visibleLines = useMemo(() => {
        let obj: ChartElements["line"] = {}
        Object.entries(props.multiline ?? {}).forEach(([, ml]) => {
            if (ml.isVisible) {
                Object.entries(ml.data).forEach(([k, dt]) => {
                    obj = {
                        ...obj,
                        [k]: {
                            ...ml,
                            data: dt
                        }
                    }
                })
            }
        })

        if (range) {
            const diff = range.to - range.from
            const left = range.from - diff
            const right = range.to + diff
            return _.pickBy(obj, (l) => {
                if (l.data.length === 0) return false
                const min = l.data[0].time as UTCTimestamp
                const max = l.data[l.data.length - 1].time as UTCTimestamp
                return min <= right && left <= max
            })
        } else return {}
    }, [range, props.multiline]);

    // const lines = useMemo(() => {
    //     return visibleLines.map((l, idx) =>
    //         <LineSeries key={l.id} options={{
    //             // customId: l.id,
    //             color: `${getColorFromSeed(idx)}${(selected && selected === l.id) ? "FF" : "66"}`,
    //             priceLineVisible: false,
    //             lastValueVisible: false
    //         }} initialData={l.data} data={l.data}/>
    //     )
    // }, [selected, visibleLines]);


    const ohlc = useMemo(() => {
        return Object.values(props.mainChartData ?? {})?.[0]?.data ?? []
    }, [props.mainChartData]);

    const markers = useMemo(() => {
        return _.sortBy(
            Object.values(props.markers ?? {})
                .filter((l) => l.isVisible)
                .map(d => d.data).flat(),
            "time"
        )
    }, [props.markers]);
    useEffect(() => {
        if (markers) {
            mainSeriesRef.current?.setMarkers(markers)
        }
    }, [markers, props.markers]);

    const linesNew = useMemo(() => {
        return Object.entries(props.lines ?? {})
            .filter(([, l]) => l.isVisible)
            .map(([, l], idx) => {
                const chunkedLines = chunkByUndefined(l.data)
                if (chunkedLines.length === 1) {
                    return [<LineSeries
                        data={chunkedLines[0]}
                        initialData={chunkedLines[0]}
                        options={{
                            lineVisible: l.isVisible,
                            priceLineVisible: false,
                            lastValueVisible: false,
                            color: l.color ?? getColorFromSeed(idx)
                        }}
                    />]
                } else return chunkedLines.map(c =>
                    <LineSeries
                        data={c}
                        initialData={c}
                        options={{
                            lineVisible: l.isVisible,
                            priceLineVisible: false,
                            lastValueVisible: false,
                            color: l.color ?? getColorFromSeed(idx)
                        }}
                    />
                )
            }).flat()
    }, [props.lines]);

    // TODO: Instead use imperative handle
    useEffect(() => {
        if (props.visibleRange) {
            chartRef.current?.timeScale().setVisibleRange(props.visibleRange)
        }
    }, [props.visibleRange]);

    const handleRef = useCallback((it) => {
        chartRef.current = it
        if (chartRef.current) {
            chartRef.current.timeScale().subscribeVisibleTimeRangeChange((p) => {
                setRange(p)
            })
            chartRef.current.subscribeCrosshairMove(p => {
                let min = Infinity
                let sel: string | undefined = undefined
                for (const [k, v] of p.seriesData) {
                    const id = (k.options() as unknown as any).customId
                    if (id) {
                        const _min = Math.abs(v.value - (k.coordinateToPrice(p.point?.y ?? 0) ?? 0))
                        if (_min < min) {
                            sel = id
                            min = _min
                        }
                    }
                }
                setSelected(sel)
            })
            // console.log("adding count", count)
            // count++
            // chartRef.current.subscribeClick(p => {
            //     // console.log("clicked", p)
            // })
        } else {
            // console.log("skipi", count)
        }

        // chartRef?.current?.priceScale("volume").applyOptions({
        //     scaleMargins: { top: 0, bottom: 0.85 }, // Volume at the top
        //     borderVisible: false,
        //     // autoScale: false
        // });
    }, [setRange, setSelected]);

    return (
        <div key={props.key} className="h-full flex flex-col">
            {/*<div ref={topBarRef} className="flex flex-col min-h-12 max-h-12">*/}
            {/*    <div className="flex flex-1" >*/}
            {/*        <Button className="text-lg" icon={<SearchOutlined />} style={{height: "100%", width: "16em", justifyContent: "start"}} type="text" iconPosition={"start"} >*/}
            {/*            NIFTY*/}
            {/*        </Button>*/}
            {/*        <Divider style={{margin: 0, height: "100%"}} type={"vertical"}/>*/}
            {/*        <Button className="text-lg" style={{height: "100%"}} type="text" >*/}
            {/*            D*/}
            {/*        </Button>*/}
            {/*        <Divider style={{margin: 0, height: "100%"}} type={"vertical"}/>*/}
            {/*    </div>*/}
            {/*    <Divider style={{margin: 0}}/>*/}
            {/*</div>*/}
            <Chart
                key={props.key}
                ref={handleRef}
                className={`h-full flex`}
                options={DefaultChartOptions}>
                <CandleStickSeries key={`${props.key}:main`} ref={(r) => {
                    mainSeriesRef.current = r
                    // Sometimes, markers are available before the series ref is created
                    mainSeriesRef.current?.setMarkers(markers)
                }} initialData={ohlc} data={ohlc}/>
                {
                    Object.entries(visibleLines).map(([k, l], idx) =>
                        <LineSeries
                            key={k}
                            data={l.data}
                            initialData={l.data}
                            options={{
                                // lineVisible: l.isVisible,
                                priceLineVisible: false,
                                lastValueVisible: false,
                                color: l.color ?? getColorFromStringSeed(k)
                            }}
                        />
                    )
                }
                {
                    linesNew/*.map((l, idx) =>
                        <LineSeries
                            data={l.data}
                            initialData={l.data}
                            options={{
                                lineVisible: l.isVisible,
                                priceLineVisible: false,
                                lastValueVisible: false,
                                color: l.color ?? getColorFromSeed(idx)
                            }}
                        />
                    )*/
                }
                {/*{*/}
                {/*    props.leftScaleLines.map(lineSeries =>*/}
                {/*        <LineSeries data={lineSeries} initialData={lineSeries} options={{priceScaleId: "left"}}/>*/}
                {/*    )*/}
                {/*}*/}
                {/*{*/}
                {/*    props.noScaleLines.map(lineSeries =>*/}
                {/*        <AreaSeries data={lineSeries} initialData={lineSeries} options={{*/}
                {/*            priceScaleId: "volume",*/}
                {/*            // color: "rgba(200, 0, 0, 0.4)",*/}
                {/*            priceLineVisible: false,*/}
                {/*            lastValueVisible: true,*/}
                {/*            invertFilledArea: true,*/}
                {/*            lineColor: "rgba(200, 0, 0, 0.3)", // Greenish line*/}
                {/*            topColor: "rgba(200, 0, 0, 0.2)", // Greenish line*/}

                {/*            // topColor: "rgba(38, 166, 154, 0.4)",*/}
                {/*            // bottomColor: "rgba(38, 166, 154, 0)"*/}
                {/*        }}/>*/}
                {/*    )*/}
                {/*}*/}
            </Chart>
        </div>

    )
}

function chunkByUndefined(arr: LineData[]): LineData[][] {
    const result: LineData[][] = [];
    let chunk: LineData[] = [];

    for (const item of arr) {
        if (item.value === undefined || item.value === null) {
            if (chunk.length > 0) {
                result.push(chunk);
                chunk = [];
            }
        } else {
            chunk.push(item);
        }
    }

    if (chunk.length > 0) {
        result.push(chunk);
    }
    return result;
}
