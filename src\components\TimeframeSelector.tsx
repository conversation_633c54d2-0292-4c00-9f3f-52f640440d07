import React, { useState } from 'react';
import { Popover, Collapse, Input, Button, List, Space, Typography } from 'antd';
import { ClockCircleOutlined, EnterOutlined } from '@ant-design/icons';
import { Timeframe } from '../types/study/Config';
const { Text } = Typography;

interface TimeframeSelectorProps {
    children: React.ReactNode;
    onTimeframeSelected: (timeframe: Timeframe) => void;
}

const TimeframeSelector: React.FC<TimeframeSelectorProps> = ({
                                                                 children,
                                                                 onTimeframeSelected
                                                             }) => {
    const [open, setOpen] = useState(false);
    const [customInput, setCustomInput] = useState('');
    const [inputError, setInputError] = useState('');

    // Predefined timeframes grouped by unit
    const timeframeGroups = {
        s: {
            label: 'Seconds',
            items: [
                { multiplier: 15, unit: 's' as const },
                { multiplier: 30, unit: 's' as const },
                { multiplier: 45, unit: 's' as const }
            ]
        },
        m: {
            label: 'Minutes',
            items: [
                { multiplier: 1, unit: 'm' as const },
                { multiplier: 3, unit: 'm' as const },
                { multiplier: 5, unit: 'm' as const },
                { multiplier: 15, unit: 'm' as const },
                { multiplier: 30, unit: 'm' as const }
            ]
        },
        h: {
            label: 'Hours',
            items: [
                { multiplier: 1, unit: 'h' as const },
                { multiplier: 2, unit: 'h' as const },
                { multiplier: 4, unit: 'h' as const }
            ]
        }
    };

    const handleTimeframeClick = (timeframe: Timeframe) => {
        onTimeframeSelected(timeframe);
        setOpen(false);
    };

    const parseCustomTimeframe = (input: string): Timeframe | null => {
        const trimmed = input.trim();
        if (!trimmed) return null;

        // Match pattern: number followed by unit (s, m, h)
        const match = trimmed.match(/^(\d+(?:\.\d+)?)(s|m|h)$/i);
        if (!match) return null;

        const multiplier = parseFloat(match[1]);
        const unit = match[2].toLowerCase() as 's' | 'm' | 'h';

        // Validate multiplier is positive
        if (multiplier <= 0) return null;

        return { multiplier, unit };
    };

    const handleCustomInputSubmit = () => {
        const timeframe = parseCustomTimeframe(customInput);
        if (timeframe) {
            onTimeframeSelected(timeframe);
            setCustomInput('');
            setInputError('');
            setOpen(false);
        } else {
            setInputError('Invalid format. Use: number + unit (e.g., 5m, 30s, 1h)');
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setCustomInput(e.target.value);
        if (inputError) setInputError('');
    };

    const handleInputKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            handleCustomInputSubmit();
        }
    };

    const formatTimeframe = (timeframe: Timeframe): string => {
        return `${timeframe.multiplier}${timeframe.unit}`;
    };

    const content = (
        <div style={{ width: 280, height: 320, display: 'flex', flexDirection: 'column' }}>
            <style>{`
        .compact-collapse .ant-collapse-header {
          padding: 4px 8px !important;
        }
        .compact-collapse .ant-collapse-content-box {
          padding: 4px 8px !important;
        }
      `}</style>
            <div style={{ flex: 1, overflowY: 'auto', marginBottom: 8 }}>
                <Collapse
                    size="small"
                    defaultActiveKey={['m']}
                    ghost
                    className="compact-collapse"
                    items={Object.entries(timeframeGroups).map(([unit, group]) => ({
                        key: unit,
                        label: (
                            <Space size="small">
                                <ClockCircleOutlined />
                                {group.label}
                            </Space>
                        ),
                        children: (
                            <List
                                size="small"
                                dataSource={group.items}
                                renderItem={(item) => (
                                    <List.Item
                                        style={{
                                            cursor: 'pointer',
                                            padding: '2px 4px',
                                            borderRadius: '4px',
                                            transition: 'background-color 0.2s'
                                        }}
                                        onClick={() => handleTimeframeClick(item)}
                                        onMouseEnter={(e) => {
                                            e.currentTarget.style.backgroundColor = '#f5f5f5';
                                        }}
                                        onMouseLeave={(e) => {
                                            e.currentTarget.style.backgroundColor = 'transparent';
                                        }}
                                    >
                                        <Text>{formatTimeframe(item)}</Text>
                                    </List.Item>
                                )}
                            />
                        )
                    }))}
                />
            </div>

            <div style={{ paddingTop: 8, borderTop: '1px solid #f0f0f0', flexShrink: 0 }}>
                <Space.Compact style={{ width: '100%' }}>
                    <Input
                        placeholder="Custom (e.g., 5m, 30s, 1h)"
                        value={customInput}
                        onChange={handleInputChange}
                        onKeyPress={handleInputKeyPress}
                        status={inputError ? 'error' : ''}
                        size="small"
                    />
                    <Button
                        type="primary"
                        size="small"
                        icon={<EnterOutlined />}
                        onClick={handleCustomInputSubmit}
                        disabled={!customInput.trim()}
                    />
                </Space.Compact>
                {inputError && (
                    <Text type="danger" style={{ fontSize: '12px', marginTop: '2px', display: 'block' }}>
                        {inputError}
                    </Text>
                )}
            </div>
        </div>
    );

    return (
        <Popover
            content={content}
            title="Select Timeframe"
            trigger="click"
            open={open}
            onOpenChange={setOpen}
            placement="bottomLeft"
        >
            {children}
        </Popover>
    );
};

export default TimeframeSelector;