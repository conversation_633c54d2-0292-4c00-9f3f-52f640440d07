import {StudyType, StudyTypeValue} from "../types/study/StudyType.ts";
import {DbBlock} from "../types/study/db/DbBlock.ts";
import {StudyParam} from "../types/study/ReadStudyParam.ts";
import {ValuesOf} from "../lib/utils/Type.ts";

type SingleValueTypeMap = {
    Constant: StudyTypeValue,
    Series: "Open" | "High" | "Low" | "Close",
    Study: StudyParam
}

type CompositeValueTypeMap = {
    Expression: Block[]
}

type OperatorTypeMap = {
    BooleanOperator: "And" | "Or",
    ComparisonOperator: "IsGreaterThan" | "IsLessThan" | "IsGreaterThanOrEqualTo" | "IsLessThanOrEqualTo" | "IsEqualTo" | "IsNotEqualTo",
    ArithmeticOperator: "Divide" | "Exponent" | "Minus" | "Multiply" | "Plus"
}

export type SingleValueType = keyof SingleValueTypeMap
export type CompositeValueType = keyof CompositeValueTypeMap
export type ValueType = SingleValueType | CompositeValueType
export type OperatorType = keyof OperatorTypeMap
export type BlockTypeMap = SingleValueTypeMap & CompositeValueTypeMap & OperatorTypeMap
export type BlockType = keyof BlockTypeMap

export interface Block<T extends BlockType = BlockType> {
    type: T,
    value: BlockTypeMap[T],
    getString(): string
    detailedString(): string
    toDb(): DbBlock<T>
}

export interface Value<T extends ValueType = ValueType> extends Block<T> {
    getValueType(): StudyType | undefined
}

export interface Operator<T extends OperatorType = OperatorType> extends Block<T> {
    precedence: number;
    associativity: "left" | "right";
    validInTypes: {
        typesForValue1: StudyType[],
        typesForValue2: StudyType[],
    }
    getValueType(typeOfValue1: StudyType, typeOfValue2: StudyType): StudyType | undefined
}

const _SingleValueTypes: ValuesOf<SingleValueType> = ["Study", "Series", "Constant"]
const _CompositeValueTypes: ValuesOf<CompositeValueType> = ["Expression"]
const _OperatorTypes: ValuesOf<OperatorType> = ["ComparisonOperator", "ArithmeticOperator", "BooleanOperator"]

export const Block = {
    SingleValueTypes: _SingleValueTypes,
    CompositeValueTypes: _CompositeValueTypes,
    ValueTypes: [..._SingleValueTypes, ..._CompositeValueTypes],
    OperatorTypes: _OperatorTypes,

    isValue(b: Block): b is Value {
        for (let i = 0; i < Block.ValueTypes.length; i++) {
            if (Block.ValueTypes[i] === b.type) return true;
        }
        return false;
    },
    isOperator(b: Block): b is Operator {
        for (let i = 0; i < Block.OperatorTypes.length; i++) {
            if (Block.OperatorTypes[i] === b.type) return true;
        }
        return false;
    }
}

export const Operator = {
    GetAssociativityPopCondition(a: "left" | "right"){
        if (a === "left")
            return (top: Operator, b: Operator) => top.precedence >= b.precedence
        else return (top: Operator, b: Operator) => top.precedence > b.precedence
    }
}
