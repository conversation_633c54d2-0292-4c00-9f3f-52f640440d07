import React, {
    useMemo,
    useCallback,
    useRef,
    useEffect,
    useState,
    Fragment, ReactNode, KeyboardEventHandler,
} from 'react'
import {Editor, Transforms, Range, createEditor, Descendant, BaseRange, BaseEditor, Node, NodeEntry} from 'slate'
import {HistoryEditor, withHistory} from 'slate-history'
import {
    Slate,
    Editable,
    ReactEditor,
    withReact,
    useSelected,
    useFocused,
    RenderLeafProps,
    RenderElementProps
} from 'slate-react'
import {createPortal} from "react-dom";
import {Block, BlockType} from "../../types/study/Block.ts";
import {AllOptions} from "./Options.ts";
import {BlockConstructors} from "../../types/study/AllBlockFunctions.ts";

const IS_MAC = false // TODO

export type Text = {
    text: string
}
export type ValueElement = {
    type: "value",
    character: string,
    children: Text[],
    block: Block<BlockType>
}
export type OperatorElement = {
    type: "operator",
    character: string,
    children: Text[],
    block: Block<BlockType>
}
export type ExpressionElement = {
    type: "expression",
    block: Block<BlockType>,
    children: (ValueElement | OperatorElement | Text | ExpressionElement)[]
}
export type BlockElement = ExpressionElement | ValueElement | OperatorElement
export type CustomElement = {
    type: "custom",
    children: (ValueElement | OperatorElement | Text | ExpressionElement)[]
}
declare module 'slate' {
    interface CustomTypes {
        Editor: BaseEditor & ReactEditor & HistoryEditor
        Element: CustomElement | ExpressionElement | OperatorElement | ValueElement
        Text: Text
    }
}
const initialValue: Descendant[] = [
    {
        type: 'custom',
        children: [
            {
                text: '',
            }
        ]
    }
]
export const Portal = ({children}: { children?: ReactNode }) => {
    return typeof document === 'object'
        ? createPortal(children, document.body)
        : null
}
const getAncestorAtSelection = (editor: Editor, level = 1) => {
    // Get the current selection
    const {selection} = editor

    if (!selection) {
        return null
    }

    // Get the path to the node at selection start
    const [, path] = Node.first({
        children: editor.children
    }, selection.anchor.path)
    // If we want immediate parent (level 1), remove last element
    // For higher levels, remove more elements from the end
    const ancestorPath = path.slice(0, -level)

    if (ancestorPath.length === 0) {
        // We've reached the root
        // return editor // TODO: Return type
        return null // TODO: Return type
    }

    // Get the ancestor node at the calculated path
    try {
        const node = Editor.node(editor, ancestorPath)
        return node // TODO: Return type
    } catch (error) {
        // Path doesn't exist
        console.log(error)
        return null
    }
}
const getCurrentWordAtSelection = (editor: Editor): BaseRange | null => {
    const {selection} = editor

    // If there's no selection, return null
    if (!selection) return null

    // Get the start point of the selection
    const start = selection.anchor

    // Get the text content of the current node
    // TODO: Types
    const node = Editor.node(editor, start.path) as NodeEntry<any>
    if (!node || typeof node[0].text !== 'string') return null

    const text = node[0].text

    // If we're at a whitespace or at the end of text with a trailing space, return null
    // if (start.offset < text.length && /\s/.test(text[start.offset])) return null
    // Check if we're at the end of text or at end of word
    const isAtEnd = start.offset === text.length || /\s/.test(text[start.offset])

    // If we're at the end, check if there's a word before
    const hasWordBefore = start.offset > 0 && !/\s/.test(text[start.offset - 1])

    // If we're at the end and there's no word before, return null
    if (isAtEnd && !hasWordBefore) return null

    // Find the start of the word by going backwards until we hit whitespace or start of text
    let wordStart = start.offset
    while (wordStart > 0 && !/\s/.test(text[wordStart - 1])) {
        wordStart--
    }

    // Find the end of the word by going forwards until we hit whitespace or end of text
    let wordEnd = isAtEnd ? start.offset : start.offset
    while (wordEnd < text.length && !/\s/.test(text[wordEnd])) {
        wordEnd++
    }

    // If we didn't find a word, return null
    if (wordStart === wordEnd) return null

    // Return the word range
    return {
        anchor: {path: start.path, offset: wordStart},
        focus: {path: start.path, offset: wordEnd}
    }
}
export const ExpressionEditor = (props: { width?: string, placeholder?: string, onChange?: (blocks: Block<BlockType>[]) => void }) => {
    const ref = useRef<HTMLDivElement | null>(null)
    const [target, setTarget] = useState<Range | null>(null)
    const [index, setIndex] = useState(0)
    const [search, setSearch] = useState('')
    const renderElement = useCallback((props: RenderElementProps) => <ElementComp {...props} />, [])
    const renderLeaf = useCallback((props: RenderLeafProps) => <Leaf {...props} />, [])
    const editor = useMemo(
        () => withMentions(withReact(withHistory(createEditor()))),
        []
    )
    const prevBlocks = useRef<Block<BlockType>[]>([]);
    const _blocks = useRef<Block<BlockType>[]>([]);
    const blocks = useMemo(() => {
        if (!editor.children) return []
        const newBlocks = extractBlocks(editor.children)
        prevBlocks.current = _blocks.current
        _blocks.current = newBlocks
        return newBlocks
    }, [editor.children]);
    useEffect(() => {
        const prevString = JSON.stringify(prevBlocks.current)
        const str = JSON.stringify(blocks)
        if (prevString !== str){
            props.onChange?.(blocks)
        }
    }, [props.onChange, blocks]);
    const chars = useMemo(() => {
        return search === "" ? [] : [...AllOptions.filter(c =>
            c.text.toLowerCase().startsWith(search.trim().toLowerCase())
        ).slice(0, 10)]
    }, [search]);

    const onKeyDown: KeyboardEventHandler<HTMLDivElement> = useCallback(
        event => {
            if (target && chars.length > 0) {
                switch (event.key) {
                    case 'ArrowDown': {
                        event.preventDefault()
                        const prevIndex = index >= chars.length - 1 ? 0 : index + 1
                        setIndex(prevIndex)
                        break
                    }
                    case 'ArrowUp': {
                        event.preventDefault()
                        const nextIndex = index <= 0 ? chars.length - 1 : index - 1
                        setIndex(nextIndex)
                        break
                    }

                    case 'Tab':
                    case 'Enter':
                        event.preventDefault()
                        Transforms.select(editor, target)
                        insertMention(editor, chars[index].getElement())
                        setTarget(null)
                        break
                    case 'Escape':
                        event.preventDefault()
                        setTarget(null)
                        break
                }
            }
            switch (event.key) {
                case 'Enter':
                    event.preventDefault()
                    break
                case "Backspace": {
                    const ancestor = getAncestorAtSelection(editor)
                    if (ancestor !== null) {
                        const [node, rp] = ancestor
                        // console.log("nde", node, rp)
                        if (node?.children.length === 1 && node?.type !== "custom") {
                            Transforms.removeNodes(editor, {at: rp, voids: true})
                            event.preventDefault()
                        }
                    }
                    break
                }
                case "ArrowRight": {
                    const ancestor = getAncestorAtSelection(editor)
                    if (ancestor !== null) {
                        const [, path] = ancestor
                        const at = Editor.after(editor, path, {unit: "character"})
                        const atL = Editor.after(editor, path)
                        if (!at) {
                            Transforms.insertNodes(editor, {text: ""}, {at: atL})
                            Transforms.move(editor, {distance: 1, unit: "offset"})
                        }
                    }
                    break
                }
            }

        },
        [chars, editor, index, target]
    )

    useEffect(() => {
        if (target && chars.length > 0) {
            // console.log("test", Editor.string(editor, target))
            const el = ref.current
            if (el === null) return
            const domRange = ReactEditor.toDOMRange(editor, target)
            const rect = domRange.getBoundingClientRect()
            // Before: PageYOffset and PageXOffset
            el.style.top = `${rect.top + window.scrollY + 24}px`
            el.style.left = `${rect.left + window.scrollX}px`
        }
    }, [chars.length, editor, index, search, target])

    return (
        <Slate
            editor={editor}
            initialValue={initialValue}
            // onValueChange={values => console.log("descendants", values)}
            onChange={() => {
                const {selection} = editor
                if (selection && Range.isCollapsed(selection)) {
                    const currentWord = getCurrentWordAtSelection(editor)
                    const target = currentWord ?? selection
                    let search = ""
                    if (currentWord === null) {
                        // console.log("no word found, only selection start")
                    } else {
                        const d = Editor.string(editor, currentWord)
                        // console.log("current word", d)
                        search = d
                    }

                    setTarget(target)
                    setSearch(search)
                    setIndex(0)
                    return
                }

                setTarget(null)
            }}
        >
            <Editable
                style={{width: props.width ?? "50%"}}
                renderElement={renderElement}
                renderLeaf={renderLeaf}
                renderPlaceholder={({attributes, children}) => (
                    <span
                        {...attributes}
                        className="absolute top-1 left-1 pointer-events-none select-none"
                        style={{
                            fontStyle: 'italic',
                            color: 'gray',
                            display: 'inline-block',
                            opacity: 'inherit'
                        }}
                    >
                        {children}
                    </span>
                )}
                onKeyDown={onKeyDown}
                className={"outline-none bg-white rounded shadow p-1 placeholder-amber-600"}
                placeholder={props.placeholder}
            />
            {target && chars.length > 0 && (
                <Portal>
                    <div
                        ref={ref}
                        style={{
                            top: '-9999px',
                            left: '-9999px',
                            position: 'absolute',
                            zIndex: 1000,
                            padding: '3px',
                            background: 'white',
                            borderRadius: '4px',
                            boxShadow: '0 1px 5px rgba(0,0,0,.2)',
                        }}
                        className={"text-black"}
                        data-cy="mentions-portal"
                    >
                        {chars.map((char, i) => (
                            <div
                                key={char.text}
                                onClick={() => {
                                    Transforms.select(editor, target)
                                    insertMention(editor, char.getElement())
                                    setTarget(null)
                                }}
                                style={{
                                    padding: '1px 3px',
                                    borderRadius: '3px',
                                    cursor: 'pointer',
                                    width: "100px",
                                    background: i === index ? '#B4D5FF' : 'transparent',
                                }}
                            >
                                {char.text}
                            </div>
                        ))}
                    </div>
                </Portal>
            )}
        </Slate>
    )
}

const withMentions = (editor: Editor) => {
    const {isInline, isVoid, markableVoid} = editor

    editor.isInline = element => {
        return element.type === 'expression' || element.type === "value" || element.type === "operator" ? true : isInline(element)
    }

    editor.isVoid = element => {
        return element.type === 'value' || element.type === "operator" ? true : isVoid(element)
    }

    editor.markableVoid = element => {
        return element.type === 'value' || element.type === "operator" ? true : markableVoid(element)
    }

    return editor
}

const insertMention = (editor: Editor, element: BlockElement) => {
    Transforms.insertNodes(editor, [element, /*{text:""}*/])
    Transforms.move(editor)
}

function extractBlocks(descendants: Descendant[]): Block<BlockType>[]{
    if (!descendants[0]) return []
    const children = descendants[0].children
    return children.filter(ch => {
        // console.log(ch)
        return ch.type === "value" || ch.type === "operator" || ch.type === "expression"
    }).map(d => d.block)
}

// Borrow Leaf renderer from the Rich Text example.
// In a real project you would get this via `withRichText(editor)` or similar.
const Leaf = ({attributes, children /*, leaf*/}: RenderLeafProps) => {
    // if (leaf.bold) {
    //     children = <strong>{children}</strong>
    // }
    //
    // if (leaf.code) {
    //     children = <code>{children}</code>
    // }
    //
    // if (leaf.italic) {
    //     children = <em>{children}</em>
    // }
    //
    // if (leaf.underline) {
    //     children = <u>{children}</u>
    // }

    return <span {...attributes}>{children}</span>
}

const ElementComp = (props: RenderElementProps) => {
    const {attributes, children, element} = props
    if (element.type === "expression") {
        return <span {...attributes}><span contentEditable={false}>(</span>{children}<span
            contentEditable={false}>)</span></span>
    }
    switch (element.type) {
        case 'value':
            return <Mention {...props} />
        case 'operator':
            return <Mention {...props} />
        default:
            return <p  {...attributes}>{children}</p>
    }
}
const Mention = (props: any) => {
    const {attributes, children, element} = props
    const selected = useSelected()
    const focused = useFocused()
    const style: React.CSSProperties = {
        padding: '3px 3px 2px',
        // margin: '0 1px',
        verticalAlign: 'baseline',
        display: 'inline-block',
        borderRadius: '4px',
        // backgroundColor: '#eee',
        fontSize: '0.9em',
        boxShadow: selected && focused ? '0 0 0 2px #00000022' : 'none',
        backgroundColor: selected && focused ? '#ddd' : undefined,
    }
    const textColor = element.type === "value" ? ( element.block.type === "Series" ? "text-purple-700" : "text-blue-700") : ""
    return (
        <span
            {...attributes}
            className={`${element.type === "value" ? `${textColor} font-semibold italic` : "text-sm"} mx-0.5`}
            contentEditable={false}
            data-cy={`mention-${element.character.replace(' ', '-')}`}
            style={style}
        >
      {/* Prevent Chromium from interrupting IME when moving the cursor */}
            {/* 1. span + inline-block 2. div + contenteditable=false */}
            <div contentEditable={false}>
        {IS_MAC ? (
            // Mac OS IME https://github.com/ianstormtaylor/slate/issues/3490
            <Fragment>
                {children}{element.character}
            </Fragment>
        ) : (
            // Others like Android https://github.com/ianstormtaylor/slate/pull/5360
            <Fragment>
                {element.character}
                {children}
            </Fragment>
        )}
      </div>
    </span>
    )
}