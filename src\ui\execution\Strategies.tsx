import React, {useState, useEffect, useCallback} from 'react';
import {Activity, Play, Pause, AlertTriangle} from 'lucide-react';
import {getStrategies, startStrategy} from "../../data/execution/Strategy.ts";
import {useNavigate} from "react-router";
import {Button, Input} from "antd";

export function Strategies() {

    const navigate = useNavigate();
    const [strategies, setStrategies] = useState<Strategy[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    const fetchStrategies = useCallback(() => {
        getStrategies().then((dt) => {
            setStrategies(dt);
        });
    }, []);

    const handleStartStrategy = useCallback((symbol: string) => {
        if (isLoading) return;
        setIsLoading(true);
        startStrategy(symbol)
            .then((data) => {
                if (!data) alert("Cannot start strategy.");
            })
            .catch((err) => {
                alert(`Unable to start strategy. ${err}`);
            })
            .finally(() => {
                fetchStrategies();
                setIsLoading(false)
            });
    }, []);

    useEffect(() => {
        fetchStrategies();
    }, []);

    return (
        <div className={"w-screen h-screen"}>
            <StrategyComp
                strategies={strategies}
                onStart={handleStartStrategy}
                onClick={(s) => {
                    navigate(`/terminal/${s.id}`)
                }}/>
        </div>
    )
}

interface Strategy {
    id: string;
    name: string;
    status: 'active' | 'paused' | 'error';
    pnl?: number;
    pnlPercent?: number;
    totalTrades?: number;
    winRate?: number;
    currentPosition?: string;
    lastUpdate?: string;
    riskLevel?: 'low' | 'medium' | 'high';
    capital?: number;
}

const StrategyComp: React.FC<{
    strategies: Strategy[],
    onClick?: (s: Strategy) => void,
    onStart?: (symbol: string) => void
}> = (props) => {
    const [symbol, setSymbol] = useState("");
    const [loading, setLoading] = useState(false);

    const getStatusColor = (status: Strategy['status']) => {
        switch (status) {
            case 'active':
                return 'text-green-600 bg-green-100';
            case 'paused':
                return 'text-yellow-600 bg-yellow-100';
            case 'error':
                return 'text-red-600 bg-red-100';
            default:
                return 'text-gray-600 bg-gray-100';
        }
    };

    const StatusIcon = ({status}: { status: Strategy['status'] }) => {
        switch (status) {
            case 'active':
                return <Play className="w-4 h-4"/>;
            case 'paused':
                return <Pause className="w-4 h-4"/>;
            case 'error':
                return <AlertTriangle className="w-4 h-4"/>;
            default:
                return <Activity className="w-4 h-4"/>;
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">Strategies</h1>
                    <p className="text-gray-600">Monitor and manage your deployed trading algorithms</p>
                </div>

                <div className={"flex mb-8 space-x-2"}>
                    <Button className={""} onClick={() => props.onStart?.(symbol)}>Start</Button>
                    <Input className={"w-40"} placeholder={"Symbol"} value={symbol}
                           onChange={(e) => setSymbol(e.target.value)}></Input>
                </div>


                {/* Strategies List */}
                <div className="bg-white rounded-lg shadow overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-semibold text-gray-900">Strategy Details</h2>
                    </div>

                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Strategy
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    P&L
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Performance
                                </th>
                                {/*<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">*/}
                                {/*    Position*/}
                                {/*</th>*/}
                                {/*<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">*/}
                                {/*    Risk*/}
                                {/*</th>*/}
                                {/*<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">*/}
                                {/*    Last Update*/}
                                {/*</th>*/}
                            </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                            {props.strategies.map((strategy) => (
                                <tr key={strategy.id} className="hover:bg-gray-50"
                                    onClick={() => props.onClick?.(strategy)}>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div className="text-sm font-medium text-gray-900">{strategy.name}</div>
                                            <div className="text-sm text-gray-500">Capital:
                                                ${strategy.capital?.toLocaleString()}</div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <span
                                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(strategy.status)}`}>
                                        <StatusIcon status={strategy.status}/>
                                        <span className="ml-1 capitalize">{strategy.status}</span>
                                      </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900">
                                            <span className={(strategy.pnl ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}>
                                              ${strategy.pnl?.toLocaleString()}
                                            </span>
                                        </div>
                                        <div
                                            className={`text-sm ${(strategy.pnlPercent ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                            {(strategy.pnlPercent ?? 0) >= 0 ? '+' : ''}{strategy.pnlPercent}%
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900">{strategy.totalTrades} trades</div>
                                        <div className="text-sm text-gray-500">{strategy.winRate}% win rate</div>
                                    </td>
                                    {/*<td className="px-6 py-4 whitespace-nowrap">*/}
                                    {/*    <div className="text-sm text-gray-900">{strategy.currentPosition}</div>*/}
                                    {/*</td>*/}
                                    {/*<td className="px-6 py-4 whitespace-nowrap">*/}
                                    {/*    <span className={`text-sm font-medium capitalize ${getRiskColor(strategy.riskLevel)}`}>*/}
                                    {/*        {strategy.riskLevel}*/}
                                    {/*    </span>*/}
                                    {/*</td>*/}
                                    {/*<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">*/}
                                    {/*    {strategy.lastUpdate}*/}
                                    {/*</td>*/}
                                </tr>
                            ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};