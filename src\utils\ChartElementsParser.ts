import {DataStore, TimeSeriesStore} from "./Store.ts";
import {
    LineData,
    OhlcData,
    SeriesMarker,
    SeriesMarkerPosition, SeriesMarkerShape,
    Time,
    UTCTimestamp,
    WhitespaceData
} from "lightweight-charts";
import {FillData} from "../chart/plugins/FillSeries.ts";
import * as _ from "lodash";
import {MutableValueStore} from "./ValueStore.ts";
import {HighlightData} from "../chart/plugins/HighlightSeries.ts";

export type MarkerData = Required<Omit<SeriesMarker<Time>, 'size'>> & Partial<Pick<SeriesMarker<Time>, 'size'>>

export type ChartElementDataItem = {
    ohlc: OhlcData,
    line: LineData,
    marker: MarkerData & { value: boolean, name: string},
    multiline: LineData & { id: string },
    fill: FillData,
    highlight: HighlightData
}

export type ChartElementType = keyof ChartElementDataItem;
export type ChartElementValue = string | number | boolean | null

type DynamicBlueprintValue = `%${number}%`

function isDynamicBlueprintValue(s: BlueprintValue): s is DynamicBlueprintValue {
    if (typeof s !== "string") return false;
    return /^%\d+(\.\d+)?%$/.test(s);
}

function getDynamicBlueprintValueIndex(s: DynamicBlueprintValue): number {
    // Remove the leading and trailing %
    const inner = s.slice(1, -1);
    const value = Number(inner);
    if (isNaN(value)) {
        throw new Error(`Invalid number inside: ${s}`);
    }
    return value;
}

export type BlueprintValue = ChartElementValue | DynamicBlueprintValue

type CommonChartElementOptions = {
    isVisible?: boolean,
    group?: string
}

export type ChartElementOptions = {
    line: {
        lineColor?: string,
        lineOpacity?: number
    } & CommonChartElementOptions,
    marker: CommonChartElementOptions,
    multiline: CommonChartElementOptions,
    fill: {
        // hex color
        fillColor: string,
        // number between 0 and 1 (both inclusive)
        fillOpacity: number
    } & CommonChartElementOptions,
    ohlc: CommonChartElementOptions,
    highlight: CommonChartElementOptions & {
        highlightColor: string;
        highlightOpacity: number;
    }
}

export type ChartElementHeader<T extends ChartElementType = ChartElementType> = {
    type: T,
    name: string,
    blueprint: { [key in keyof ChartElementDataItem[T]]: ChartElementValue },
    staticOpts: ChartElementOptions[T]
}

export type ChartElementHeaderWithStore<T extends ChartElementType = ChartElementType> = ChartElementHeader<T> & {
    store: DataStore<ChartElementDataItem[T]>,
    optionsStore: MutableValueStore<ChartElementOptions[T]>
}

export function checkHeaderType<T extends ChartElementType>(type: T, header: ChartElementHeader): header is ChartElementHeader<T> {
    return type === header.type
}

export function checkHeaderWithStoreType<T extends ChartElementType>(type: T, header: ChartElementHeaderWithStore): header is ChartElementHeaderWithStore<T> {
    return type === header.type
}

// Denotes One Chart element
export type RawChartElement = ChartElementValue | ChartElementValue[]

const Builder: { [key in ChartElementType]: (header: ChartElementHeader<key>, element: RawChartElement) => ChartElementDataItem[key] | WhitespaceData | undefined } = {
    line(header: ChartElementHeader<"line">, element: RawChartElement): ChartElementDataItem["line"] | WhitespaceData {
        const value = getChartElementValue(header.blueprint.value, element) as number | null
        return {
            time: getChartElementValue(header.blueprint.time, element) as UTCTimestamp,
            value: value === null ? undefined : value
        }
    },
    // TODO: Handle whitespace data.
    ohlc(header: ChartElementHeader<"ohlc">, element: RawChartElement): ChartElementDataItem["ohlc"] {
        return {
            close: getChartElementValue(header.blueprint.close, element) as number,
            high: getChartElementValue(header.blueprint.high, element) as number,
            low: getChartElementValue(header.blueprint.low, element) as number,
            open: getChartElementValue(header.blueprint.open, element) as number,
            time: getChartElementValue(header.blueprint.time, element) as UTCTimestamp
        };
    },
    marker(header: ChartElementHeader<"marker">, element: RawChartElement): ChartElementDataItem["marker"] | WhitespaceData | undefined {
        if (element === null) return undefined;
        else return {
            value: getChartElementValue(header.blueprint.value, element) as boolean,
            time: getChartElementValue(header.blueprint.time, element) as UTCTimestamp,
            id: getChartElementValue(header.blueprint.id, element) as string,
            color: getChartElementValue(header.blueprint.color, element) as string,
            position: getChartElementValue(header.blueprint.position, element) as SeriesMarkerPosition,
            shape: getChartElementValue(header.blueprint.shape, element) as SeriesMarkerShape,
            text: getChartElementValue(header.blueprint.text, element) as string,
            name: header.name
        }
    },
    highlight(header: ChartElementHeader<"highlight">, element: RawChartElement): ChartElementDataItem["highlight"] | WhitespaceData | undefined {
        return {
            value: getChartElementValue(header.blueprint.value, element) as boolean,
            highlightColor: getChartElementValue(header.blueprint.highlightColor!, element) as string,
            time: getChartElementValue(header.blueprint.time, element) as UTCTimestamp
        };
    },
    // TODO: Handle whitespace data
    fill(header: ChartElementHeader<"fill">, element: RawChartElement): ChartElementDataItem["fill"] | WhitespaceData | undefined {
        return {
            value1: getChartElementValue(header.blueprint.value1, element) as number,
            value2: getChartElementValue(header.blueprint.value2, element) as number,
            time: getChartElementValue(header.blueprint.time, element) as UTCTimestamp
        };
    },
    multiline(header: ChartElementHeader<"multiline">, element: RawChartElement): ChartElementDataItem["multiline"] | WhitespaceData | undefined {
        if (element === null) return undefined;

        return {
            id: getChartElementValue(header.blueprint.id, element) as string,
            value: getChartElementValue(header.blueprint.value, element) as number,
            time: getChartElementValue(header.blueprint.time, element) as UTCTimestamp
        };
    }
}

export class ChartElementsParser {

    headers: ChartElementHeaderWithStore[]

    constructor(headers: ChartElementHeaderWithStore[]) {
        this.headers = headers;
    }

    // parse update
    parseUpdate(elements: RawChartElement[]) {
        this.headers.forEach((h, idx) => {
            const element = elements[idx];
            // @ts-expect-error - Type error expected here
            const dataItem = Builder[h.type](h, element);
            if (dataItem !== undefined) {
                // @ts-expect-error - Type error expected here
                h.store.update(dataItem)
            }
        })
    }

    // parse data
    parseData(data: RawChartElement[][]){
        this.headers.forEach((h, idx) => {
            const elementData: ChartElementDataItem[ChartElementType][] = [];
            data.forEach((elements) => {
                const element = elements[idx];
                // @ts-expect-error - Type error expected here
                const dataItem = Builder[h.type](h, element);
                if (dataItem !== undefined) {
                    // @ts-expect-error - Type error expected here
                    elementData.push(dataItem);
                }
            })
            h.store.setData(elementData)
        })
    }
}

function getChartElementValue(blueprintValue: BlueprintValue, raw: RawChartElement): ChartElementValue {
    if (isDynamicBlueprintValue(blueprintValue)) {
        const idx = getDynamicBlueprintValueIndex(blueprintValue);
        if (_.isArray(raw)) {
            const out = raw[idx]
            if (out === undefined) throw new Error(`Parser e1`)
            return out
        } else {
            if (idx === 0)
                return raw
            else throw new Error(`Parser e2`)
        }
    } else {
        return blueprintValue
    }
}