
export type ValueOrPercent = {
    type: "Value" | "%",
    value: number
}

export const ValueOrPercent = {
    getSignedDisplayName: (value: ValueOrPercent) => {
        const sign = value.value > 0 ? "+" : "";
        switch (value.type) {
            case "Value":
                return `${sign}${value.value}`
            case "%":
                return `${sign}${value.value}%`;
        }
    }
}