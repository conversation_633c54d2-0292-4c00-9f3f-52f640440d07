import {
    But<PERSON>,
    List,
    Space, Splitter, Typography,
} from "antd";
import {useMainDispatch, useMainSelector} from "../../redux/MainStore.ts";
import {addEntry, addExit, remove, update} from "../../redux/strategy/Strategy.ts";
import {Entry, Exit} from "../../types/strategy/Actions.ts";
import {ExitComp} from "./ExitComp.tsx";
import {EntryComp} from "./EntryComp.tsx";
import {useMemo, useState} from "react";
import {ExpressionBuilder} from "../../components/condition/ConditionBuilder.tsx";
import {ExpressionEditor} from "../study/Mention.tsx";
import {Flow} from "./Flow.tsx";


export function StrategySection() {

    const dispatch = useMainDispatch();
    const actions = useMainSelector(s => s.strategy.actions)
    const entryIds = useMemo(() => {
        return actions.filter(a => a.type === "Entry").map(a => a._id)
    }, [actions]);

    return (
        <div className="h-full w-full" style={{height: "100%"}}>
            <Splitter>
                <Splitter.Panel size={"25%"}>
                    <ConditionsSection/>
                </Splitter.Panel>
                <Splitter.Panel className={"p-4"}>

                    <Flow/>

                    {/*<div className="p-4">*/}
                    {/*    <Space.Compact className={""}>*/}
                    {/*        <Button onClick={() => dispatch(addEntry())}> Add Entry </Button>*/}
                    {/*        /!*TODO: Handle if first is undefined*!/*/}
                    {/*        <Button onClick={() => dispatch(addExit(entryIds[0]))}> Add Exit </Button>*/}
                    {/*    </Space.Compact>*/}
                    {/*    <List*/}
                    {/*        grid={{gutter: 16, column: 2}}*/}
                    {/*        dataSource={actions}*/}
                    {/*        renderItem={(action, idx) => (*/}
                    {/*            <List.Item>*/}
                    {/*                {*/}
                    {/*                    action.type === "Entry" &&*/}
                    {/*                    // TODO: Prefer a type guard over `as` keyword*/}
                    {/*                    <EntryComp*/}
                    {/*                        entry={action as Entry}*/}
                    {/*                        onChange={item => dispatch(update({idx, item}))}*/}
                    {/*                        onRemove={() => dispatch(remove(idx))}*/}
                    {/*                    />*/}
                    {/*                }*/}
                    {/*                {*/}
                    {/*                    action.type === "Exit" &&*/}
                    {/*                    // TODO: Prefer a type guard over `as` keyword*/}
                    {/*                    <ExitComp*/}
                    {/*                        // entryTargets={actions.find(a => a.name === (action as Exit).forEntry)?.targets ?? []}*/}
                    {/*                        // entryIds={entryNames}*/}
                    {/*                        exit={action as Exit}*/}
                    {/*                        onChange={item => dispatch(update({idx, item}))}*/}
                    {/*                        onRemove={() => dispatch(remove(idx))}*/}
                    {/*                    />*/}
                    {/*                }*/}
                    {/*            </List.Item>*/}
                    {/*        )}*/}
                    {/*    />*/}
                    {/*</div>*/}

                </Splitter.Panel>
            </Splitter>
        </div>
    )
}


// Add study, Add a condition
function ConditionsSection(){
    const [conditionModalOpen, setConditionModalOpen] = useState(false)

    return (
        <div className={"/*flex flex-col items-center*/ p-4"}>
            <Typography.Title level={5}>Conditions</Typography.Title>
            <Space className={"pt-8"}>
                {/*<Button size={"small"}>Add Study</Button>*/}
                <Button size={"small"} type={"primary"} onClick={() => setConditionModalOpen(!conditionModalOpen)}>Add Condition</Button>
            </Space>
            <ConditionBuilder title={"Conditions"} open={conditionModalOpen} onCancel={() => setConditionModalOpen(false)}/>
        </div>
    )
}