export type StudyType = "Number" | "Boolean" | "String" | "Color" | "Position" | "Shape"
type StudyTypePrimitiveMap = {
    Number: number,
    Boolean: boolean,
    String: string,
    Color: string,
    Time: string,
    Position: "AboveBar" | "BelowBar",
    Shape: "ArrowUp" | "ArrowDown"
}

export type StudyParam<T extends StudyType = StudyType> = {
    paramName: string,
    studyDepId: string,
    valueType: T
}
export type StudyTypeValue<T extends StudyType = StudyType> = {
    type: T,
    value: StudyTypePrimitiveMap[T]
}
type SeriesElement = "Open" | "Close" | "High" | "Low" | "Index" | "IsFirstBar"

export type LogicalOperation ="And" | "Or"
export type ArithmeticOperation = "Divide" | "Exponent" | "Minus" | "Multiply" | "Plus"
export type ComparisonOperation ="IsGreaterThan" | "IsLessThan" | "IsGreaterThanOrEqualTo" | "IsLessThanOrEqualTo" | "IsEqualTo" | "IsNotEqualTo"
type SingleValueTypeMap = {
    Constant: {
        value: StudyTypeValue
    },
    Series: {
        value: Value.Single.SeriesValue.SeriesElement,
        offset?: number
    },
    Study: {
        value: StudyParam,
        offset?: number
    }
}
type CompositeValueTypeMap = {
    Expression: {
        value: Block<BlockType>[]
    }
}
type OperatorTypeMap = {
    BooleanOperator: { value: Operator.Logical.LogicalOperation },
    ComparisonOperator: { value: Operator.Comparison.ComparisonOperation },
    ArithmeticOperator: { value: Operator.Arithmetic.ArithmeticOperation },
}

export type BlockTypeMap = SingleValueTypeMap & CompositeValueTypeMap & OperatorTypeMap

export type Block<T extends BlockType> = {
    type: T
} & BlockTypeMap[T]