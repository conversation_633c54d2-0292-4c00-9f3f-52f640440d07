import {BlockConstructors} from "../../types/study/Block.ts";
import {BlockElement} from "./Mention.tsx";
import {Value} from "../../types/study/Values.ts";
import SeriesElement = Value.Single.SeriesValue.SeriesElement;
import {Operator} from "../../types/study/Operators.ts";
import ArithmeticOperation = Operator.Arithmetic.ArithmeticOperation;
import LogicalOperation = Operator.Logical.LogicalOperation;
import SeriesElements = Value.Single.SeriesValue.SeriesElements;
import AltNameToComparisonOperationMap = Operator.Comparison.AltNameToComparisonOperationMap;

const SeriesOptions = ["Close", "Open", "High", "Low"].map(str =>
    ({
        text: str,
        getElement: (): BlockElement => ({
            type: "value",
            character: str,
            children: [{text: ""}],
            block: BlockConstructors.Series({value: str as SeriesElement})
        }),
    })
)
const SeriesOffsetOptions = SeriesElements.map((str) =>
    ({
        text: `${str}-1`,
        getElement: (): BlockElement => ({
            type: "value",
            character: `${str}-1`,
            children: [{text: ""}],
            block: BlockConstructors.Series({value: str as SeriesElement, offset: 1})
        }),
    })
)
const ArithmeticOptions = ["+", "-", "*", "/"].map(str =>
    ({
        text: str,
        getElement: (): BlockElement => ({
            type: "operator",
            character: str,
            children: [{text: ""}],
            block: BlockConstructors.ArithmeticOperator({value: str as ArithmeticOperation})
        }),
    })
)
const ComparisonOptions = [">", "<", ">=", "<="].map(str =>
    ({
        text: str,
        getElement: (): BlockElement => ({
            type: "operator",
            character: str,
            children: [{text: ""}],
            block: BlockConstructors.ComparisonOperator({value: AltNameToComparisonOperationMap[str as keyof typeof AltNameToComparisonOperationMap]})
        }),
    })
)
const BooleanOptions = ["and", "or"].map(str =>
    ({
        text: str,
        getElement: (): BlockElement => ({
            type: "operator",
            character: str,
            children: [{text: ""}],
            block: BlockConstructors.BooleanOperator({value: str as LogicalOperation})
        }),
    })
)

const StudyOptions = ["ema"].map(str =>
    ({
        text: str,
        getElement: (): BlockElement => ({
            type: "value",
            character: str,
            children: [{text: ""}],
            block: BlockConstructors.Study({value: str})
        }),
    })
)

const OperatorOptions = [...ArithmeticOptions, ...ComparisonOptions, ...BooleanOptions]
export const AllOptions = [...SeriesOptions, ...OperatorOptions, ...SeriesOffsetOptions, ...StudyOptions]