import {
    BitmapCoordinatesR<PERSON>ingScope,
    CanvasRenderingTarget2D,
} from 'fancy-canvas';
import {
    CustomData,
    ICustomSeriesPaneRenderer,
    UTCTimestamp,
} from 'lightweight-charts';
import {
    CustomSeriesOptions,
    customSeriesDefaultOptions,
    CustomSeriesPricePlotValues,
    ICustomSeriesPaneView,
    PaneRendererCustomData,
    WhitespaceData,
    Time,
} from 'lightweight-charts';

export interface HighlightData extends CustomData {
    value: boolean;
    time: UTCTimestamp;
    highlightColor?: string; // Custom color for individual data points
}

interface HighlightBarItem {
    x: number;
    value: boolean;
    highlightColor?: string;
}

export interface HighlightSeriesOptions extends CustomSeriesOptions {
    highlightColor: string;
    highlightOpacity: number;
    isVisible: boolean;
}

export const defaultOptions: HighlightSeriesOptions = {
    ...customSeriesDefaultOptions,
    highlightColor: 'rgba(255, 255, 0, 0.3)',
    highlightOpacity: 0.3,
    isVisible: true,
} as const;

export class HighlightSeriesPlugin<TData extends HighlightData>
    implements ICustomSeriesPaneView<Time, TData, HighlightSeriesOptions>
{
    _renderer: HighlightSeriesRenderer<TData>;

    constructor() {
        this._renderer = new HighlightSeriesRenderer();
    }

    priceValueBuilder(): CustomSeriesPricePlotValues {
        return [];
    }

    isWhitespace(data: TData | WhitespaceData): data is WhitespaceData {
        return (data as Partial<TData>).value === undefined;
    }

    renderer(): HighlightSeriesRenderer<TData> {
        return this._renderer;
    }

    update(
        data: PaneRendererCustomData<Time, TData>,
        options: HighlightSeriesOptions
    ): void {
        this._renderer.update(data, options);
    }

    defaultOptions(): HighlightSeriesOptions {
        return defaultOptions;
    }
}

export class HighlightSeriesRenderer<TData extends HighlightData>
    implements ICustomSeriesPaneRenderer
{
    _data: PaneRendererCustomData<Time, TData> | null = null;
    _options: HighlightSeriesOptions | null = null;

    draw(
        target: CanvasRenderingTarget2D,
        // priceConverter: PriceToCoordinateConverter
    ): void {
        target.useBitmapCoordinateSpace(scope =>
            this._drawImpl(scope)
        );
    }

    update(
        data: PaneRendererCustomData<Time, TData>,
        options: HighlightSeriesOptions
    ): void {
        this._data = data;
        this._options = options;
    }

    private _drawImpl(
        renderingScope: BitmapCoordinatesRenderingScope,
        // priceToCoordinate: PriceToCoordinateConverter
    ): void {
        if (
            this._data === null ||
            this._data.bars.length === 0 ||
            this._data.visibleRange === null ||
            this._options === null ||
            !this._options.isVisible
        ) {
            return;
        }

        const ctx = renderingScope.context;
        const yTop = 0;
        const height = renderingScope.bitmapSize.height;
        const halfWidth = (renderingScope.horizontalPixelRatio * (this._data.barSpacing)) / 2;
        const cutOff = -halfWidth - 1;
        const maxX = renderingScope.bitmapSize.width;

        const bars: HighlightBarItem[] = this._data.bars
            .slice(this._data.visibleRange.from, this._data.visibleRange.to)
            .map(bar => ({
                x: bar.x,
                value: bar.originalData.value,
                highlightColor: bar.originalData.highlightColor,
            }));

        ctx.save();
        bars.forEach(bar => {
            if (!bar.value) return;

            const xScaled = bar.x * renderingScope.horizontalPixelRatio;
            if (xScaled < cutOff) return;

            ctx.fillStyle = bar.highlightColor && bar.highlightColor.trim() !== ''
                ? bar.highlightColor
                : this._options!.highlightColor;
            ctx.globalAlpha = this._options!.highlightOpacity;

            const x1 = Math.max(0, Math.round(xScaled - halfWidth));
            const x2 = Math.min(maxX, Math.round(xScaled + halfWidth));
            ctx.fillRect(x1, yTop, x2 - x1, height);
        });
        ctx.restore();
    }
}