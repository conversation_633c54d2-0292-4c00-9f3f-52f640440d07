import { useEffect, useRef, useState, useCallback } from 'react';

interface ChildConfig {
    id: string;
    percent?: number; // If undefined, will fill remaining space
    minWidth?: number; // Optional minimum width in pixels
    maxWidth?: number; // Optional maximum width in pixels
}

interface CalculatedWidth {
    id: string;
    width: number;
}

interface UseResponsiveWidthsOptions {
    gap?: number; // Gap between children in pixels
    padding?: number; // Parent padding that should be subtracted
}

export const useResponsiveWidths = (
    children: ChildConfig[],
    options: UseResponsiveWidthsOptions = {}
) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [parentWidth, setParentWidth] = useState<number>(0);
    const [calculatedWidths, setCalculatedWidths] = useState<CalculatedWidth[]>([]);

    const { gap = 0, padding = 0 } = options;

    // Calculate widths based on parent width and child configurations
    const calculateWidths = useCallback((containerWidth: number) => {
        if (containerWidth <= 0 || children.length === 0) {
            setCalculatedWidths([]);
            return;
        }

        // Available width after accounting for gaps and padding
        const totalGaps = Math.max(0, children.length - 1) * gap;
        const availableWidth = containerWidth - totalGaps - (padding * 2);

        // Separate fixed percentage children from fill children
        const fixedChildren = children.filter(child => child.percent !== undefined);
        const fillChildren = children.filter(child => child.percent === undefined);

        // Calculate width for fixed percentage children
        let usedWidth = 0;
        const results: CalculatedWidth[] = [];

        // Process fixed percentage children first
        fixedChildren.forEach(child => {
            let width = (availableWidth * child.percent!) / 100;

            // Apply min/max constraints
            if (child.minWidth !== undefined) {
                width = Math.max(width, child.minWidth);
            }
            if (child.maxWidth !== undefined) {
                width = Math.min(width, child.maxWidth);
            }

            usedWidth += width;
            results.push({
                id: child.id,
                width
            });
        });

        // Calculate remaining width for fill children
        const remainingWidth = Math.max(0, availableWidth - usedWidth);
        const fillChildWidth = fillChildren.length > 0 ? remainingWidth / fillChildren.length : 0;

        // Process fill children
        fillChildren.forEach(child => {
            let width = fillChildWidth;

            // Apply min/max constraints
            if (child.minWidth !== undefined) {
                width = Math.max(width, child.minWidth);
            }
            if (child.maxWidth !== undefined) {
                width = Math.min(width, child.maxWidth);
            }

            results.push({
                id: child.id,
                width
            });
        });

        // Sort results to match original children order
        const sortedResults = children.map(child =>
            results.find(result => result.id === child.id)!
        ).filter(Boolean);

        setCalculatedWidths(sortedResults);
    }, [children, gap, padding]);

    // ResizeObserver to watch parent width changes
    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        const resizeObserver = new ResizeObserver((entries) => {
            for (const entry of entries) {
                const { width } = entry.contentRect;
                setParentWidth(width);
                calculateWidths(width);
            }
        });

        resizeObserver.observe(container);

        // Initial calculation
        const initialWidth = container.offsetWidth;
        setParentWidth(initialWidth);
        calculateWidths(initialWidth);

        return () => {
            resizeObserver.disconnect();
        };
    }, [calculateWidths]);

    // Recalculate when children configuration changes
    useEffect(() => {
        if (parentWidth > 0) {
            calculateWidths(parentWidth);
        }
    }, [children, calculateWidths, parentWidth]);


    return {
        containerRef,
        calculatedWidths
    };
};