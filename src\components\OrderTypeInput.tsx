import {Select, Space, Typography} from "antd";
import {ValueOrPercentInput} from "./CascaderNumberInput.tsx";
import {OrderType} from "../types/strategy/OrderDetails.ts";
import {ValueOrPercent} from "../types/ValueOrPercent.ts";


type OrderTypeInputProps = {
    orderType: OrderType,
    onChange: (orderType: OrderType) => void,
}

export function OrderTypeInput(props: OrderTypeInputProps) {

    const setType = (t: OrderType["type"]) => {
        const res: OrderType = t === "Limit" ? {type: "Limit", value: {type: "Value", value: 0}} : {type: "Market"}
        props.onChange(res)
    }
    const setValue = (value: ValueOrPercent | null) => {
        if (value !== null)
            props.onChange({
                type: "Limit",
                value: value
            })
    }

    return (
        <Space direction={"vertical"}>
            <Space.Compact>
                <Select value={props.orderType.type} style={{}} onChange={setType}>
                    <Select.Option key={"Market"} value={"Market"}>{"Market"}</Select.Option>
                    <Select.Option key={"Limit"} value={"Limit"}>{"Limit"}</Select.Option>
                </Select>
                {
                    props.orderType.type === "Limit" &&
                    <ValueOrPercentInput value={props.orderType.value} onValueChange={setValue}/>
                }
            </Space.Compact>
            <Typography.Text type={"secondary"}>
                {
                    props.orderType.type === "Limit" ?
                        "Set a fixed value or percentage to adjust the limit price relative to the current market price."
                        :
                        "The order will be placed at the current market price."
                }
            </Typography.Text>
        </Space>
    )
}