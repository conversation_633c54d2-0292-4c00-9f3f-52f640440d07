import React from 'react';

interface LiveConnectionDotProps {
    size?: 'sm' | 'md' | 'lg';
    isConnected?: boolean;
    className?: string;
}

export const LiveConnectionDot: React.FC<LiveConnectionDotProps> = ({
                                                                        size = 'md',
                                                                        isConnected = true,
                                                                        className = ''
                                                                    }) => {
    const sizeClasses = {
        sm: 'w-2 h-2',
        md: 'w-3 h-3',
        lg: 'w-4 h-4'
    };

    const pulseAnimation = isConnected ? 'animate-pulse' : '';
    const dotColor = isConnected ? 'bg-green-500' : 'bg-gray-400';

    return (
        <div className={`flex items-center space-x-2 ${className}`}>
            <div className="relative">
                <div
                    className={`${sizeClasses[size]} ${dotColor} rounded-full ${pulseAnimation}`}
                />
            </div>
            <span className="text-sm text-green-600 ">
                {isConnected ? 'Running' : 'Disconnected'}
            </span>
        </div>
    );
};