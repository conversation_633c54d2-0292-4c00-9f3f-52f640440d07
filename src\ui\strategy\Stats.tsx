import {LineData, UTCTimestamp} from "lightweight-charts";
import {FC, useMemo, useState} from "react";
import {ResponsiveBar} from "@nivo/bar";
import {useBacktestApi} from "../../data/BacktestOld.ts";
import {ResponsiveLineCanvas} from "@nivo/line";
import ScatterPlot<PERSON>hart from "./ScatterPlot.tsx";
import _ from "lodash";
import {Statistic} from "antd";
import {ChartItem} from "./Charts.tsx";
import {TestChart} from "../plots/Plot.tsx";

// data -> line data for overall pnl and closed pnl
// trades -> closed pnl

// TODO categories -> overall pnl distribution, win/lose trades distribution
// cumulative realized vs unrealized line chart
// bullet chart, max pnl, min pnl, average pnl, and current pnl
// stacked lines (like seasonality) trades pnl, by time / candles

export function Stats() {
    const response = useBacktestApi(undefined)

    const dt = useMemo(() => {
        if (response === undefined || response.closedPositions === undefined) return data
        return convertTradingDataForScatterPlot(response.closedPositions)
    }, [response]);

    const maxY = useMemo(() => {
        const max = _.maxBy(dt[0].data, "y")?.y
        return max === undefined ? undefined : max * 1.05
    }, [dt]);

    return (
        <div>
            <div style={{width: "100%"}}>
                <div style={{display: "flex", width: "100%"}} className={"px-2"}>
                    <Statistic
                        valueStyle={{color: "#3f8600"}}
                        className={"p-2 flex-1"}
                        title={"Net Profit"}
                        prefix={(response?.overallPnl ?? 0) > 0 ? "+" : "-"}
                        value={response?.overallPnl}
                    />
                    <Statistic
                        valueStyle={{color: ""}}
                        className={"p-2 flex-1"}
                        title={"Max Drawdown"}
                        value={response?.maxDrawdown}
                    />
                </div>
                <div style={{display: "flex", width: "100%"}} className={"px-2"}>
                    <Statistic
                        valueStyle={{color: ""}}
                        className={"p-2 flex-1"}
                        title={"Total Closed Trades"}
                        value={response?.closedTrades}
                    />
                    <Statistic
                        valueStyle={{color: ""}}
                        className={"p-2 flex-1"}
                        title={"Profitable Trades"}
                        value={`${response?.profitableTrades} / ${response?.closedTrades}`}
                        suffix={<span className={"text-sm"}>{`${response?.profitPercent}%`}</span>}
                    />
                </div>
                <div style={{display: "flex", width: "100%"}} className={"px-2"}>
                    <Statistic
                        valueStyle={{color: ""}}
                        className={"p-2 flex-1"}
                        title={"Avg. Profit"}
                        value={response?.avgProfit}
                    />
                    <Statistic
                        valueStyle={{color: ""}}
                        className={"p-2 flex-1"}
                        title={"Avg. Loss"}
                        value={response?.avgLoss}
                    />
                </div>
            </div>
            <ScatterPlotChart
                data={dt}
                xAxis={{label: 'X-Axis',}}
                yAxis={{label: 'Y-Axis', max: maxY}}
                height={400}
                width={600}
            />
            {/*<PnlLineChart series1={response?.chartElements?.line.overallPnl ?? []} series2={response?.chartElements?.line.realizedPnl ?? []}/>*/}
            <ChartItem type={"line"} data={testData}/>
            <TestChart/>
        </div>
    )
}


const data = [
    {
        "id": "PnL vs Time Taken",
        "data": [
            {"x": 900.0 / 60, "y": 96.05},
            {"x": 60.0 / 60, "y": -28.65},
            {"x": 300.0 / 60, "y": 0.12},
            {"x": 360.0 / 60, "y": -24.03},
            {"x": 780.0 / 60, "y": -15.29},
            {"x": 180.0 / 60, "y": -22.84},
            {"x": 780.0 / 60, "y": -4.2},
            {"x": 780.0 / 60, "y": 8.95},
            {"x": 1680.0 / 60, "y": 27.02},
            {"x": 1020.0 / 60, "y": 11.82},
            {"x": 420.0 / 60, "y": 4.9},
            {"x": 480.0 / 60, "y": -3.92},
            {"x": 840.0 / 60, "y": -7.32},
            {"x": 120.0 / 60, "y": -21.78},
            {"x": 300.0 / 60, "y": -7.2},
            {"x": 900.0 / 60, "y": -0.73}
        ]
    }
];

export const testData = data.map(d => {
    const arr = d.data.map((p, i, array) => ({
        x: i,
        y: p.y,
        y2: p.y - i,
        c: p.y < array[i + 1]?.y ? "green" : "red",
        isWinning: p.y > 0,
        isLosing: p.y <= 0
    }))
    return ({
        ...d,
        data: arr,
        max: _.maxBy(arr, "y"),
        min: _.minBy(arr, "y"),
        average: _.mean(_.map(arr, "y")),
    });
})

// console.log("td", testData)
export const testData2 = data.map(d => ({
    ...d,
    data: d.data.map((p, i) => ({x: i, y: (p.y) + (p.x % 50), y2: p.y - i}))
}))

/**
 * Calculates the price level above which PnL was present for a given percentage of time
 * @param data Array of price points with time and value
 * @param percentile Percentage of time PnL should be above the returned value (0-100)
 * @returns The price level at the specified percentile
 */
function calculatePnLThreshold(data: LineData[], percentile: number): number {
    // Input validation
    if (percentile < 0 || percentile > 100) {
        throw new Error('Percentile must be between 0 and 100');
    }
    if (data.length === 0) {
        throw new Error('Data array cannot be empty');
    }

    // Extract values and sort them in ascending order
    const sortedValues = data
        .map(point => point.value)
        .sort((a, b) => a - b);

    // Calculate the index for the desired percentile
    // For 90% of values above, we need the 10th percentile (100 - 90 = 10)
    const inversedPercentile = 100 - percentile;
    const index = Math.floor((inversedPercentile / 100) * (sortedValues.length - 1));

    return sortedValues[index];
}

// Find the price level above which PnL was 90% of the time
// const threshold = calculatePnLThreshold(sampleData, 90);
// console.log(`PnL was above ${threshold} 90% of the time`);

export function convertTradingDataForScatterPlot(rawData: Array<{ timeTaken: number, cumulativePnl: number }>) {
    // Map each raw data point to the format expected by the scatter plot
    const formattedData = rawData.map(item => ({
        x: item.timeTaken,
        y: item.cumulativePnl
    }));

    // Return a single series with all data points
    return [
        {
            id: "PnL vs Time Taken",
            data: formattedData
        }
    ];
}