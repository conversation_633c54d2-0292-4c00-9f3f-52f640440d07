import * as Plot from "@observablehq/plot";
import {useEffect, useRef} from "react";

export default function PlotFigure({ options }: {options: Plot.PlotOptions}) {

    const ref = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const r = ref.current;
        const chart = Plot.plot({ ...options })
        if (r != null) r.append(chart)
        return () => chart.remove()
    }, [options]);

    return (
        <div ref={ref}>
        </div>
    )
}