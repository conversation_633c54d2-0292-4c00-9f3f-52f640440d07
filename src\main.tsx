import { createRoot } from "react-dom/client";
import "./index.css";
import { Provider } from "react-redux";
import { MainStore } from "./redux/MainStore.ts";
import { BuilderNew } from "./ui/strategy/BuilderNew.tsx";
import { BrowserRouter, Route, Routes } from "react-router";
import { Strategies } from "./ui/execution/Strategies.tsx";
import { Backtest } from "./ui/terminal/Backtest.tsx";
import { Realtime } from "./ui/terminal/Realtime.tsx";
import { Playground } from "./ui/playground.tsx";
import "@radix-ui/themes/styles.css";
import { Theme } from "@radix-ui/themes";
import WaitList from "./components/waitlist/WaitList.tsx";

createRoot(document.getElementById("root")!).render(
  <Provider store={MainStore}>
    <Theme>
      <BrowserRouter>
        <Routes>
          <Route index element={<div></div>} />
          <Route path="builder" element={<BuilderNew />} />
          <Route path="/terminal/:id" element={<Realtime />} />
          <Route path="/terminal" element={<Playground />} />
          <Route path="/backtest/:symbol" element={<Backtest />} />
          <Route path="strategy" element={<Strategies />} />
          <Route path="/waitlist" element={<WaitList />} />
        </Routes>
      </BrowserRouter>
    </Theme>
  </Provider>
);
