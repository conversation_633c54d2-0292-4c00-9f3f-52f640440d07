import React, { useState } from "react";

const WaitList = () => {
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email.trim()) {
      // Here you would typically send the email to your backend
      console.log("Email submitted:", email);
      setIsSubmitted(true);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Navigation Header */}
      <nav className="flex items-center justify-between px-6 py-4 border-b border-gray-800">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
            <span className="text-black font-bold text-sm">K</span>
          </div>
          <span className="text-xl font-bold">KIRO</span>
          <span className="text-xs bg-purple-600 px-2 py-1 rounded text-white">
            PREVIEW
          </span>
        </div>

        <div className="hidden md:flex items-center space-x-8">
          <a
            href="#"
            className="text-gray-300 hover:text-white transition-colors"
          >
            CHANGELOG
          </a>
          <a
            href="#"
            className="text-gray-300 hover:text-white transition-colors"
          >
            PRICING
          </a>
          <a
            href="#"
            className="text-gray-300 hover:text-white transition-colors"
          >
            DOCS
          </a>
          <div className="relative group">
            <button className="text-gray-300 hover:text-white transition-colors flex items-center">
              RESOURCES
              <svg
                className="w-4 h-4 ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
          </div>
          <button className="bg-gray-800 hover:bg-gray-700 px-4 py-2 rounded transition-colors">
            Join Waitlist
          </button>
        </div>
      </nav>

      {/* Main Content */}
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-80px)] px-6">
        <div className="max-w-2xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            Thank you for your amazing
            <br />
            response to Kiro!
          </h1>

          <p className="text-gray-400 text-lg md:text-xl mb-12 max-w-xl mx-auto">
            Kiro is seeing unprecedented demand. Join our waitlist and we'll
            notify you as soon as access is available.
          </p>

          {!isSubmitted ? (
            <form onSubmit={handleSubmit} className="w-full max-w-md mx-auto">
              <div className="flex flex-col sm:flex-row gap-4">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className="flex-1 px-4 py-3 bg-gray-900 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent transition-all"
                  required
                />
                <button
                  type="submit"
                  className="px-8 py-3 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:ring-offset-2 focus:ring-offset-black"
                >
                  Join Waitlist
                </button>
              </div>
            </form>
          ) : (
            <div className="w-full max-w-md mx-auto">
              <div className="bg-green-900/20 border border-green-700 rounded-lg p-6">
                <div className="flex items-center justify-center mb-4">
                  <svg
                    className="w-12 h-12 text-green-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-green-400 mb-2">
                  You're on the list!
                </h3>
                <p className="text-gray-300">
                  Thanks for joining our waitlist. We'll notify you as soon as
                  Kiro becomes available.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Background decoration */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-600/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600/10 rounded-full blur-3xl"></div>
      </div>
    </div>
  );
};

export default WaitList;
