
export type ExpiryType = "Weekly" | "Monthly" | "Quarterly"
export const ExpiryTypesList: ExpiryType[] = ["Weekly", "Monthly", "Quarterly"]

type Map = {
    "Spot": undefined,
    "Future": {
        expiry: ExpiryType,
        expiryOffset: number // TODO: Merge expiry and offset
    },
    "CE": {
        expiry: ExpiryType,
        expiryOffset: number,
        criteria: unknown // TODO: Use criteria type
    },
    "PE": {
        expiry: ExpiryType,
        expiryOffset: number,
        criteria: unknown // TODO: Use criteria type
    }
}

export type InstrumentType = keyof Map

export type Instrument<T extends InstrumentType = InstrumentType> = {
    type: T,
    value: Map[T] // TODO: use spread operator
}
export const Instrument = {
    getShortDisplayName(inst: Instrument): string {
        switch (inst.type){
            case "Spot":
                return "Spot"
            case "Future":
                return "Fut"
            case "CE":
                return "Call"
            case "PE":
                return "Put"
        }
    }
}

const DefaultConstructors: { [k in InstrumentType]: () => Instrument<k> } = {
    "Spot": () => ({
        type: "Spot",
        value: undefined
    }),
    "Future": () => ({
        type: "Future",
        value: {
            expiry: "Monthly",
            expiryOffset: 0
        }
    }),
    "CE": () => ({
        type: "CE",
        value: {
            expiry: "Weekly",
            expiryOffset: 0,
            criteria: null
        }
    }),
    "PE": () => ({
        type: "PE",
        value: {
            expiry: "Weekly",
            expiryOffset: 0,
            criteria: null
        }
    })
}

export const Constructors = {
    DefaultInstrument: (type: InstrumentType): Instrument => {
        return DefaultConstructors[type]()
    }
}

export function isDerivative(instrument: Instrument): instrument is Instrument<"Future" | "CE" | "PE"> {
    return instrument.type !== "Spot"
}