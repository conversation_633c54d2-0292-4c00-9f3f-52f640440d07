import React, {forwardRef, ReactNode, useImperativeHandle, useLayoutEffect, useRef} from "react";
import {ApiWrapper} from "./api-wrapper.ts";
import {
    IChartApi, ICustomSeriesPaneView,
    ISeriesApi,
    SeriesDataItemTypeMap,
    SeriesOptionsMap,
    Time
} from "lightweight-charts";
import {createNullableContext, useNotNullContext} from "./react-helpers.ts";
import {ChartApiContext} from "./Chart.tsx";
import {FillData, FillSeriesOptions, FillSeriesPlugin} from "../plugins/FillSeries.ts";
import {MultilineData, MultilineSeriesOptions, MultilineSeriesPlugin} from "../plugins/MultiLineSeries.ts";
import {HighlightData, HighlightSeriesOptions, HighlightSeriesPlugin} from "../plugins/HighlightSeries.ts";

type SeriesApi<T extends keyof SeriesOptionsMap , HorzScaleItem = Time, TData = SeriesDataItemTypeMap<HorzScaleItem>[T], TOptions = SeriesOptionsMap[T]> = 
    ApiWrapper<ISeriesApi<T, HorzScaleItem, TData,TOptions>>

export type SeriesProps<T extends keyof SeriesOptionsMap, HorzScaleItem = Time, TData = SeriesDataItemTypeMap<HorzScaleItem>[T], TOptions = SeriesOptionsMap[T]> = {
    initialData?: TData[],
    data?: TData[],
    options?: Partial<TOptions>,
    children?: ReactNode
}
function makeSeries<T extends keyof SeriesOptionsMap, HorzScaleItem extends Time = Time, TData = SeriesDataItemTypeMap<HorzScaleItem>[T], TOptions = SeriesOptionsMap[T]>(createApiFunc: (thisArg: IChartApi) => () => ISeriesApi<T, HorzScaleItem, TData, TOptions>)
:[React.Context<SeriesApi<T, HorzScaleItem, TData, TOptions> | null>, React.ForwardRefExoticComponent<React.PropsWithoutRef<SeriesProps<T, HorzScaleItem, TData, TOptions>> & React.RefAttributes<ISeriesApi<T, HorzScaleItem, TData, TOptions>>>] {
    const Context = createNullableContext<SeriesApi<T, HorzScaleItem, TData, TOptions>>()
    const Component= forwardRef((props: SeriesProps<T, HorzScaleItem, TData, TOptions>, ref:React.ForwardedRef<ISeriesApi<T, HorzScaleItem, TData, TOptions>>) => {
         const chartApi = useNotNullContext(ChartApiContext);
         const seriesApiRef = useRef(new ApiWrapper<ISeriesApi<T, HorzScaleItem, TData, TOptions>>(
             ()=> {
                 const api = createApiFunc(chartApi.getApi()).call(chartApi.getApi())
                 api.setData(props.initialData ?? [])
                 // console.log("c series api")
                 return api
             },
             () => {
                 // @ts-expect-error: type error - Resolve later
                 chartApi.getApi().removeSeries(seriesApiRef.current.getApi())
                 // console.log("removing series api")
             }
         ))



         useLayoutEffect(() => {
             const currentRef = seriesApiRef.current;
             currentRef.getApi();
             return () => currentRef.free();
         }, []);

         useLayoutEffect(() => {
             if (props.options)
                 seriesApiRef.current.getApi().applyOptions(props.options);
         });

        useLayoutEffect(() => {
            if (props.data)
                seriesApiRef.current.getApi().setData(props.data)
        }, [props.data]);

         useImperativeHandle(ref, () => seriesApiRef.current.getApi(), []);

         return (
             <>
                 <Context.Provider value={seriesApiRef.current}>
                     {props.children}
                 </Context.Provider>
             </>
         )
     })
    return [Context, Component];
}

export const [AreaSeriesApiContext, AreaSeries] = makeSeries<"Area">(chartApi => chartApi.addAreaSeries)
export const [LineSeriesApiContext, LineSeries] = makeSeries<"Line">(chartApi => chartApi.addLineSeries)
export const [BarSeriesApiContext, BarSeries] = makeSeries<"Bar">(chartApi => chartApi.addBarSeries)
export const [BaseLineSeriesApiContext, BaseLineSeries] = makeSeries<"Baseline">(chartApi => chartApi.addBaselineSeries)
export const [CandleStickSeriesApiContext, CandleStickSeries] = makeSeries<"Candlestick">(chartApi => chartApi.addCandlestickSeries)
export const [HistogramSeriesApiContext, HistogramSeries] = makeSeries<"Histogram">(chartApi => chartApi.addHistogramSeries)

// @ts-expect-error type error, resolve later
export const [FillSeriesApiContext, FillSeries] = makeSeries<"Custom", Time, FillData, FillSeriesOptions>(chartApi => () => chartApi.addCustomSeries(new FillSeriesPlugin()))
// @ts-expect-error type error, resolve later
export const [HighlightSeriesApiContext, HighlightSeries] = makeSeries<"Custom", Time, HighlightData, HighlightSeriesOptions>(chartApi => () => chartApi.addCustomSeries(new HighlightSeriesPlugin()))
// @ts-expect-error type error, resolve later
export const [MultilineSeriesApiContext, MultilineSeries] = makeSeries<"Custom", Time, MultilineData, MultilineSeriesOptions>(chartApi => () => chartApi.addCustomSeries(new MultilineSeriesPlugin()))
export function createCustomSeries(createView: () => ICustomSeriesPaneView){
    return makeSeries<"Custom">(chartApi => () => chartApi.addCustomSeries(createView()))
}