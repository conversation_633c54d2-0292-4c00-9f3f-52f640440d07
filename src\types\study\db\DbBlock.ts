import {StudyTypeValue} from "../StudyType";
import {Value} from "../Values";
import {Operator} from "../Operators";
import {BlockType} from "../Block";
import {StudyParam} from "../ReadStudyParam";

// TODO: Try to raise CTE if new block is added in Block.ts
type SingleValueTypeMap = {
    Constant: {
        value: StudyTypeValue
    },
    Series: {
        value: Value.Single.SeriesValue.SeriesElement,
        offset?: number
    },
    Study: {
        value: StudyParam,
        offset?: number
    }
}
type CompositeValueTypeMap = {
    Empty: {},
    BinaryComposite: {
        value: {
            op: Value.Composite.BinaryComposite.Operations,
            lhs: DbBlock<BlockType>,
            rhs: DbBlock<BlockType>
        }
    },
    Expression: {
        value: DbBlock<BlockType>[]
    },
    ConditionalValue: {
        value: {
            conditions: {
                condition: DbBlock<BlockType>,
                value: DbBlock<BlockType>
            }[],
            elseValue: DbBlock<BlockType>
        }
    }
}
type OperatorTypeMap = {
    BooleanOperator: { value: Operator.Logical.LogicalOperation },
    ComparisonOperator: { value: Operator.Comparison.ComparisonOperation },
    ArithmeticOperator: { value: Operator.Arithmetic.ArithmeticOperation },
}

export type DbBlockTypeMap = SingleValueTypeMap & CompositeValueTypeMap & OperatorTypeMap

export type DbBlock<T extends BlockType> = {
    type: T
} & DbBlockTypeMap[T]