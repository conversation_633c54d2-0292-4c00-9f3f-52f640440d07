import {useState} from 'react';
import * as Toggle from '@radix-ui/react-toggle';
import {Eye, EyeOff} from 'lucide-react';
import StyleOptionsDialog from "./StyleOptionsDialog.tsx";
import {Style, StyleTypeOptionsMap} from '../data/Terminal.ts';

interface StyleComponentProps {
    style: Style;
    isVisible: boolean;
    onToggled: (toggled: boolean) => void;
    onSave: (style: Style) => void;
}

function StyleComponent({style, isVisible, onToggled, onSave}: StyleComponentProps) {
    // const getIcon = (type: string) => {
    //     switch (type) {
    //         case 'Line': return <TrendingUp className="w-4 h-4 text-gray-500" />;
    //         case 'Marker': return <TriangleIcon className="w-4 h-4 text-gray-500" />;
    //         case 'Highlight': return <Square className="w-4 h-4 text-gray-500" />;
    //         case 'Fill': return <PaintBucket className="w-4 h-4 text-gray-500" />;
    //         default: return null;
    //     }
    // };

    const [open, setOpen] = useState(false);
    const [visible, setVisible] = useState(isVisible);

    const renderLineOptions = (options: StyleTypeOptionsMap['line']) => (
        <div className="flex items-center gap-2">
            <div
                className="w-4 h-4 rounded border "
                style={{backgroundColor: options.lineColor, opacity: options.lineOpacity}}
            />
        </div>
    );

    const renderMarkerOptions = () => (
        <div className="flex items-center gap-2 w-4"></div>
    );

    const renderHighlightOptions = (options: StyleTypeOptionsMap['highlight']) => (
        <div className="flex items-center gap-2">
            <div
                className="w-4 h-4 rounded border "
                style={{backgroundColor: options.highlightColor, opacity: options.highlightOpacity}}
            />
        </div>
    );

    const renderFillOptions = (options: StyleTypeOptionsMap['fill']) => (
        <div className="flex items-center gap-2">
            <div
                className="w-4 h-4 rounded border "
                style={{backgroundColor: options.fillColor, opacity: options.fillOpacity}}
            />
        </div>
    );

    const renderOptions = () => {
        switch (style.type) {
            case 'line':
                return renderLineOptions(style.options);
            case 'marker':
                return renderMarkerOptions();
            case 'highlight':
                return renderHighlightOptions(style.options);
            case 'fill':
                return renderFillOptions(style.options);
            default:
                return null;
        }
    };

    return (
        <StyleOptionsDialog initialStyle={style} onSave={onSave}>

            <div className="flex items-center justify-between gap-2 p-2  border-b border-gray-200 cursor-pointer "
                 onClick={() => setOpen(!open)}>
                <div className="flex items-center gap-2">
                    {/*{getIcon(style.type)}*/}
                    <span className="text-sm font-medium text-gray-800">{style.type}</span>
                </div>
                <div className="flex-1"></div>
                {renderOptions()}
                <Toggle.Root
                    pressed={visible}
                    onPressedChange={() => {
                        setVisible(!visible);
                        onToggled(!visible);
                    }}
                    className="p-1 rounded hover:bg-gray-100 focus:outline-none"
                >
                    {visible ? (
                        <Eye className="w-4 h-4 text-blue-500" strokeWidth={1.5}/>
                    ) : (
                        <EyeOff className="w-4 h-4 text-gray-500" strokeWidth={1.5}/>
                    )}
                </Toggle.Root>
            </div>
        </StyleOptionsDialog>
    );
}


export {StyleComponent};