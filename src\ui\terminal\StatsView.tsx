import {ArrowDownOutlined} from "@ant-design/icons";
import {Card, Statistic} from "antd";
import {ChartAreaInteractive} from "./stats/LineChart.tsx";

export function StatsView() {
    return (
        <div className={"w-full"}>
            <div className={"w-full flex p-4 space-x-4"}>
                <div className={"bg-white rounded-lg text-zinc-700 text-sm"}>
                    <ul className={"p-2 space-y-1"}>
                        <li className={"px-2 py-1 cursor-pointer bg-[#7e92f4] rounded-lg text-white"}>Overall</li>
                        <li className={"px-2 py-1 cursor-pointer hover:bg-[#7e92f4] rounded-lg hover:text-white"}>Profit & Loss</li>
                        <li className={"px-2 py-1 cursor-pointer hover:bg-[#7e92f4] rounded-lg hover:text-white"}>Profit Distribution</li>
                        <li className={"px-2 py-1 cursor-pointer hover:bg-[#7e92f4] rounded-lg hover:text-white"}>Statistics</li>
                    </ul>
                </div>
                <div className={"flex-1 space-y-6"}>
                    {/* Metrics */}
                    <div>
                        <h1 className={"text-zinc-700 text-xl mb-2 ml-1"}>Overall</h1>
                        <div className={"flex w-full space-x-4"}>
                            <Card className={"flex-1"}>
                                <Statistic
                                    title="Overall Pnl"
                                    value={"+27,149.75"}
                                    precision={2}
                                    valueStyle={{color: '#059669'}}
                                />
                            </Card>
                            <Card className={"flex-1"}>
                                <Statistic
                                    title="Profit Factor"
                                    value={1.34}
                                />
                            </Card>
                            <Card className={"flex-1"}>
                                <Statistic
                                    title="Profit Expectancy"
                                    value={"+4,513"}
                                    precision={2}
                                    suffix={<span className={"text-sm"}>per trade</span>}
                                />
                            </Card>
                            <Card className={"flex-1"}>
                                <Statistic
                                    title="Max. Drawdown"
                                    value={"5,125.00"}
                                    precision={2}
                                    valueStyle={{color: '#cf1322'}}
                                    prefix={<ArrowDownOutlined/>}
                                />
                            </Card>
                        </div>
                    </div>
                    <h1 className={"text-zinc-700 text-xl mb-2 ml-1"}>Profit & Loss</h1>
                    {/*<ChartAreaInteractive />*/}
                </div>
            </div>
        </div>
    );
}

