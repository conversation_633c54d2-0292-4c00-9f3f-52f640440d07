import {Tag} from "antd";
import {PencilIcon, XMarkIcon} from "@heroicons/react/24/solid";
import {TradeData} from "../../data/Types.ts";
import {ActionData, ActionType} from "../../data/Types.ts";

type TradeProps = {
    trade: TradeData,
    allowActions: boolean,
    pnl?: number,
    onClose?: () => void
}

export function TradeComp(props: TradeProps) {
    return (
        <div>
            <div className={"w-full bg-[#f3f4fb] rounded-lg border flex flex-col"}>
                <div className={"bg-[#ffffff] px-2 py-1 border-b rounded-t-lg flex"}>
                    <h1 className={"text-zinc-700 text-sm pt-0.5"}>{props.trade.tradeInstanceId}</h1>
                    <div className="flex-1"></div>
                    <span className={"text-emerald-600"}>{props.pnl?.toFixed(2) ?? "-"}</span>
                </div>
                <div>
                    {
                        props.trade.actions.map((action) => (
                            <div>
                                <div className={"bg-[#fafafc] p-1.5 border-b  flex items-center"}>
                                    {/*TODO: Tag by action type*/}
                                    <ActionTag action={action}/>
                                    <h1 className={"text-zinc-700 text-sm "}>{action.actionId}</h1>
                                    <div className="flex-1"></div>
                                    {/*TODO: Status by action state*/}
                                    <h1 className={"text-zinc-400 font-semibold text-xs"}>Completed</h1>
                                </div>
                                <div className={"px-2 py-1 text-zinc-600 text-xs font-semibold space-y-2"}>
                                    {
                                        action.positions.map((position) => (
                                            <div className={"flex"}>
                                                <span className={"flex-1"}>{position.name}</span>
                                                <span className={"flex-1 text-end"}>{position.quantity} Qty.</span>
                                                <span className={"flex-1 text-end"}>{position.price ? `@ ${position.price}` : "-"}</span>
                                            </div>
                                        ))
                                    }
                                    {/*<div className={"flex"}>*/}
                                    {/*    <span className={"flex-1"}>Nifty 25200 CE</span>*/}
                                    {/*    <span className={"flex-1 text-end"}>300 Qty.</span>*/}
                                    {/*    <span className={"flex-1 text-end"}>@ 50.00</span>*/}
                                    {/*</div>*/}
                                </div>
                            </div>
                        ))
                    }
                    {/*<div>*/}
                    {/*    <div className={"bg-[#fafafc] p-1.5 border-y  flex items-center"}>*/}
                    {/*        <Tag color="red">SL</Tag>*/}
                    {/*        <h1 className={"text-zinc-700 text-sm"}>Base SL</h1>*/}
                    {/*        <div className="flex-1"></div>*/}
                    {/*        <h1 className={"text-orange-400 font-semibold text-xs"}>Active</h1>*/}
                    {/*    </div>*/}
                    {/*    <div className={"px-2 py-1 text-zinc-600 text-xs font-semibold space-y-2"}>*/}
                    {/*        <div className={"flex"}>*/}
                    {/*            <span className={"flex-1"}>Nifty 25000 CE</span>*/}
                    {/*            <span className={"flex-1 text-end"}>150 Qty.</span>*/}
                    {/*            <span className={"flex-1 text-end "}>-</span>*/}
                    {/*        </div>*/}
                    {/*        <div className={"flex"}>*/}
                    {/*            <span className={"flex-1"}>Nifty 25200 CE</span>*/}
                    {/*            <span className={"flex-1 text-end"}>-300 Qty.</span>*/}
                    {/*            <span className={"flex-1 text-end "}>-</span>*/}
                    {/*        </div>*/}
                    {/*    </div>*/}
                    {/*</div>*/}
                </div>
            </div>
            <div className={"mt-1"}>
                {
                    props.allowActions &&
                    <>
                        <div
                            className={"hover:bg-zinc-700/20 inline-block text-zinc-500  px-1 py-1 rounded-full"}>
                            <PencilIcon className={"w-4 h-4 "}/>
                        </div>
                        <div
                            className={"hover:bg-zinc-700/20 inline-block text-zinc-500  px-1 py-1 rounded-full"}>
                            <XMarkIcon className={"w-4 h-4 "} onClick={() => props.onClose?.()}/>
                        </div>
                    </>
                }
            </div>
        </div>
    );
}

const ActionTagColorMap: Record<ActionType, string> = {
    entry: "blue",
    exit: "orange",
    sl: "orange",
    close: "orange"
}
const ActionTagTitleMap: Record<ActionType, string> = {
    entry: "Entry",
    exit: "Exit",
    sl: "SL",
    close: "Close"
}

export function ActionTag(props: { action: ActionData }) {
    return (
        <>
            {
                <Tag color={ActionTagColorMap[props.action.type]}>{ActionTagTitleMap[props.action.type]}</Tag>
            }
        </>
    )
}
