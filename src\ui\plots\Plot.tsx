import PlotFigure from "./PlotFigure.tsx";
import * as Plot from "@observablehq/plot";
import {testData} from "../strategy/Stats.tsx";
import {ChartItem, MarkRenderFunctions} from "./ChartItem.tsx";
import {useMemo} from "react";

export function PlotChart(props: { chartItem: ChartItem, width?: number }){

    const marks = useMemo(() => {
        // @ts-expect-error later
        let marks = props.chartItem.marks.map(m => MarkRenderFunctions[m.type](m, props.chartItem.data)).flat()
        if (props.chartItem.gridX){
            marks = [Plot.gridX(), ...marks]
        }
        if (props.chartItem.gridY){
            marks = [Plot.gridY(), ...marks]
        }
        return marks
    }, [props.chartItem]);

    return (
        <PlotFigure options={{
            marginLeft: 60,
            marginRight: 60,
            width: props.width,
            marks: marks,
            style: {
                fontSize: "0.8rem"
            },
            x: {
                ...(props.chartItem.x ?? {}),
                ticks: props.chartItem.tickStepX === undefined ? undefined : props.chartItem.data.map(dt => dt.i).filter((_, i) => (i +1) % props.chartItem.tickStepX! === 0),
            },

        }}/>
    )
}

export function TestChart(){
     return (
         <div className="">
             <PlotChart chartItem={{
                 data: testData[0].data,
                 marks: [
                     {
                         type: "bar",
                         options: { x: "x", y: "y", fill: "green", filter: "isWinning" },
                     },
                     {
                         type: "bar",
                         options: { x: "x", y: "y", fill: "red", filter: "isLosing" },
                     },
                     {
                         type: "area",
                         options: { x: "x", y: "y", fill:"green", fillOpacity: 0.2 },
                     },
                     {
                         type: "line",
                         options: { x: "x", y: "y" },
                     },
                     {
                         type:"marker",
                         options: { value: 0 }
                     }
                 ],
                 gridY: true,
                 gridX: true
             }}/>
             <PlotFigure
                 options={{
                     marks: [
                         Plot.gridX(),
                         Plot.gridY(),
                         Plot.ruleY([0]),
                         Plot.differenceY(testData[0].data,  { x: "x", y:"y", positiveFill: "green", negativeFill: "red", tip: true }),
                     ]
                 }}
             />
             <PlotFigure
                 options={{
                     x: { tickFormat: "d", interval: 1 },
                     marks: [
                         Plot.crosshairX(testData[0].data, {x: "x", y:"y"}),
                         Plot.ruleY([0]),
                         // Plot.barY(testData[0].data, { x: "x", y: "y", fill: "red", filter: (d) => d.y <= 0 }),
                         // Plot.barY(testData[0].data, { x: "x", y: "y", fill: "green", filter: (d) => d.y > 0 }),
                         Plot.lineY(testData[0].data, {  x: "x", y: "y",z: "stroke", stroke: "c", curve:"linear", strokeLinecap: "round"}),
                         Plot.gridX(),
                         Plot.gridY(),
                     ]
                 }}
             />
         </div>
     );
 }