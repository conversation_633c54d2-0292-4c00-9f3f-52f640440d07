import {useMemo} from "react";
import {InputNumber, Select} from "antd";
import {Quantity, QuantityTypesList} from "../types/strategy/OrderDetails.ts";
import {ValueOrPercent} from "../types/ValueOrPercent.ts";

type Limit = {
    max?: number,
    min?: number,
}

type CascaderNumberInputProps<T extends string> = {
    types: T[],
    value: { type: T, value: number },
    getLimit?: (t: T) => Limit | undefined,
    onValueChange: (data: { type: T, value: number } | null ) => void,
    placeHolder?: string,
    disabled?: boolean
}

export function CascaderNumberInput<T extends string>(props: CascaderNumberInputProps<T>){
    // const [type, setType] = useState<T>(props.types[0]);
    // const [num, setNum] = useState<number | null>(null)

    const limit = useMemo(() => {
        return props.getLimit?.(props.value.type) ?? {}
    }, [props, props.value.type]);

    // useEffect(() => {
    //     // props.onValueChange( props.value.value != null && num >= (limit?.min ?? -Infinity) && num <= (limit?.max ?? Infinity) ? { type, value: num } : null)
    //     props.onValueChange({})
    // }, [props, type, num, limit]);

    const setType = (t: T) => {
        props.onValueChange({ type: t, value: props.value.value })
    }
    const setValue = (v: number | null) => {
        props.onValueChange({type: props.value.type, value: v ?? props.value.value})
    }

    return (
        <>
            <InputNumber style={{ width: 200}}
                         {...props}
                         placeholder={props.placeHolder}
                         min={limit?.min}
                         max={limit?.max}
                         keyboard
                         changeOnWheel
                         value={props.value.value}
                         onChange={setValue}
                         addonAfter={
                             <Select disabled={props.disabled} value={props.value.type} style={{}} onChange={setType}>
                                 {
                                     props.types.map(t =>
                                         <Select.Option key={t} value={t}>{t}</Select.Option>
                                     )
                                 }
                             </Select>
                         }
            />
        </>
    )
}



type QuantityInputProps = {
    quantity: Quantity,
    onChange: (q: Quantity | null) => void
}

export function QuantityInput(props: QuantityInputProps){
    return (
        <CascaderNumberInput value={props.quantity} placeHolder={"Not set"} types={QuantityTypesList} onValueChange={props.onChange} getLimit={() => ({ min: 0 })}/>
    )
}


const ValueOrPercentTypes: ValueOrPercent["type"][] = ["Value", "%"]

const getDefaultLimits = (type: ValueOrPercent["type"]): Limit | undefined => {
    if (type === "%") return { min: -100, max: 100 }
    else return undefined
}
const positiveOnlyLimits = (type: ValueOrPercent["type"]): Limit | undefined => {
    if (type === "%") return { min: 0, max: 100 }
    else return { min: 0 }
}

type ValueOrPercentProps = {
    value: ValueOrPercent,
    positiveOnly?: boolean,
    onValueChange: (v: ValueOrPercent | null) => void,
    disabled?: boolean
}
export function ValueOrPercentInput(props: ValueOrPercentProps){
    return (
        <CascaderNumberInput {...props} placeHolder={"Offset"} types={ValueOrPercentTypes} value={props.value}
                             onValueChange={props.onValueChange}
                             getLimit={props.positiveOnly ? positiveOnlyLimits :getDefaultLimits}/>
    )
}
