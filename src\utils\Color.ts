// Predefined list of aesthetically pleasing colors
const CURATED_COLORS =[
    "#59A14F", // Green
    "#E15759", // Red
    "#4E79A7", // Blue
    "#F28E2B", // Orange
    "#B07AA1", // Purple
    "#9C755F", // <PERSON>
    "#E377C2", // Pink
    "#BAB0AC", // Gray
    "#EDC948", // Yellow
    "#76B7B2"  // Cyan
];
/**
 * Returns a hex color string from a curated list based on input number
 * @param seed - Any number to use as a seed for color selection
 * @returns A hex color string
 */
const getColorFromSeed = (seed: number): string => {
    // Ensure positive number and handle potential negative inputs
    const positiveNum = Math.abs(seed);

    // Use modulo to get an index within the array bounds
    const index = positiveNum % CURATED_COLORS.length;

    return CURATED_COLORS[index];
};

const getColorFromStringSeed = (seed: string): string => {
    // Convert string to a numeric hash
    let hash = 0;
    for (let i = 0; i < seed.length; i++) {
        hash = (hash * 31 + seed.charCodeAt(i)) >>> 0; // Ensure positive number
    }

    // Use modulo to get an index within the array bounds
    const index = hash % CURATED_COLORS.length;

    return CURATED_COLORS[index];
};


/**
 * Returns a random color from the curated list
 * @returns A hex color string
 */
const getRandomColor = (): string => {
    const randomIndex = Math.floor(Math.random() * CURATED_COLORS.length);
    return CURATED_COLORS[randomIndex];
};

export { getColorFromSeed, getRandomColor, getColorFromStringSeed };