import {useEffect, useRef, useState} from "react";
import {ChartElementHeaderX, ChartElementsParserX, ChartElementType} from "../utils/ChartElements.ts";
import {ChartElementsStoreOld, TimeSeriesStore} from "../utils/Store.ts";
import useWebSocket from "react-use-websocket";
import {OhlcData} from "lightweight-charts";

type Row = (number | string | boolean | Row)[]

type RealtimeMessageTypeMap = {
    C: {
        studyHeaders: ChartElementHeaderX[],
        actionHeaders: ChartElementHeaderX[],
    },
    SD: Row[],
    SU: Row,
}
type RealtimeMessageType = keyof RealtimeMessageTypeMap
type RealtimeMessage<K extends RealtimeMessageType = RealtimeMessageType> = {
    type: K,
    data: RealtimeMessageTypeMap[K]
}
function checkType<K extends RealtimeMessageType>(type: K, msg: RealtimeMessage): msg is RealtimeMessage<K> {
    return msg.type == type
}

// study updates (chart elements), action updates (chart elements), pnl updates (data), order updates (data), logs (data)

export function useRealtime(){

    const [studyParser, setStudyParser] = useState(new ChartElementsParserX([]));
    const [actionParser, setActionParser] = useState(new ChartElementsParserX([]));
    const [stores, setStores] = useState<ChartElementsStoreOld>({
        line: {}, marker: {}, ohlc: {}, fill: {}
    });
    const storeRef = useRef<typeof stores>(stores);
    const parserRef = useRef<typeof studyParser>(studyParser);


    const [socketUrl, setSocketUrl] = useState('ws://127.0.0.1:8080/realtime');
    const { sendMessage, lastMessage, readyState } = useWebSocket(socketUrl);


    useEffect(() => {
        storeRef.current = stores;
        console.log(stores)
    }, [stores])

    useEffect(() => {
        parserRef.current = studyParser
    }, [studyParser]);

    useEffect(() => {
        console.log("lm", lastMessage?.data)
        const studyParser = parserRef.current
        if (lastMessage !== null) {
            const message: RealtimeMessage = JSON.parse(lastMessage.data);
            if (checkType("C", message)){
                setStudyParser(new ChartElementsParserX(message.data.studyHeaders))
                setActionParser(new ChartElementsParserX(message.data.actionHeaders))
            }
            else if (checkType("SD", message)){
                const elements = studyParser.parse(message.data as any)
                Object.entries(elements).forEach(([k, obj]) => {
                    const type = k as Exclude<ChartElementType, "multiline">
                    Object.entries(obj).forEach(([id, item]) => {
                        const currentStore = storeRef.current[type][id]
                        const { data, ...rest } = item
                        if (currentStore === undefined){
                            const newStore = new TimeSeriesStore<OhlcData>(i => i.time as number)
                            newStore.setData(data)
                            newStore.setExtras(rest)
                            storeRef.current = {
                                ...storeRef.current,
                                [type]: {
                                    ...storeRef.current[type],
                                    [id]: newStore
                                }
                            }
                            setStores(storeRef.current)
                        }
                        else {
                            currentStore.setData(data)
                            currentStore.setExtras(rest)
                        }
                    })
                })
            }
            else if (checkType("SU", message)){
                const elements = studyParser.parse([message.data] as any)
                Object.entries(elements).forEach(([k, obj]) => {
                    const type = k as Exclude<ChartElementType, "multiline">
                    Object.entries(obj).forEach(([id, item]) => {
                        const currentStore = storeRef.current[type][id]
                        if (currentStore === undefined){
                            const newStore = new TimeSeriesStore<OhlcData>(i => i.time as number)
                            newStore.update(item.data[0])
                            storeRef.current = {
                                ...storeRef.current,
                                [type]: {
                                    ...storeRef.current[type],
                                    [id]: newStore
                                }
                            }
                            setStores(storeRef.current)
                        }
                        else {
                            currentStore.update(item.data[0])
                        }
                    })
                })
            }
        }
    }, [lastMessage]);

    return stores;
}


