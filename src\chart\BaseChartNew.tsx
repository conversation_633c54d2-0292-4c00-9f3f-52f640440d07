// Base Chart -> Only chart pane, candlestick data, styles

import React, {forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {IChartApi, ISeriesApi, LineData, OhlcData} from "lightweight-charts";
import {Chart} from "./lib/Chart.tsx";
import {CandleStickSeries, LineSeries} from "./lib/Series.tsx";
import {DefaultChartOptions} from "./DefaultOptions.ts";
import {getColorFromSeed} from "../utils/Color.ts";
import {ChartElements, ChartElementTypeMap} from "../utils/ChartElements.ts";
import _ from "lodash";

type BaseChartProps = {
    key: React.Key,
    width?: number,
    height?: number,

    lines?: ChartElements["line"],
    markers?: ChartElements["marker"],
}

export type CustomInputHandle = {
    setOhlcData(ohlcData: ChartElementTypeMap["ohlc"]): void;
    setLines(lines: ChartElements["line"][string]): void;
    updateOhlc(ohlc: ChartElementTypeMap["ohlc"][number]): void;
    updateLine(id: string, item: LineData): void;
};

export const TVChart = forwardRef<CustomInputHandle, BaseChartProps>((props, ref) => {

    const topBarRef = useRef<HTMLDivElement>(null);
    const chartRef = useRef<IChartApi>();
    const mainSeriesRef = useRef<ISeriesApi<"Candlestick"> | null>(null);
    const linesRef = useRef<Record<string, ISeriesApi<"Line">>>({});

    const [ohlcData, setOhlcData] = useState<OhlcData[]>([]);
    const [linesData, setLinesData] = useState<ChartElements["line"][string][]>([]);

    useImperativeHandle(ref, () => ({
        setOhlcData(ohlcData: ChartElementTypeMap["ohlc"]) {
            setOhlcData(ohlcData);
        },
        setLines(line: ChartElements["line"][string]): void {
            setLinesData((lines) => {
                const x =  [...lines, {
                    ...line,
                    data: line.data.map((i) => {
                        if (i.value === null){
                            return {
                                value: undefined,
                                time: i.time
                            }
                        }
                        else return i
                    })
                }]
                console.log("updated lines", x)
                return x
            })
        },
        updateOhlc(ohlc: ChartElementTypeMap["ohlc"][number]): void {
            mainSeriesRef.current?.update(ohlc)
        },
        updateLine(id: string, item: LineData) {
            console.log(`update ${id}`, item, linesRef.current[id], linesRef.current)
            item = item.value === null ? {
                    value: undefined,
                    time: item.time
            } : item
            linesRef.current[id]?.update(item);
        }
    }), [setLinesData, setOhlcData]);

    // TODO: Review
    useEffect(() => {
        if (!props.height && !props.width) return
        let obj = {}
        if (props.height) obj = {...obj, height: Math.max(props.height - (topBarRef.current?.clientHeight ?? 0), 0)}
        if (props.width) obj = {...obj, width: props.width}
        chartRef.current?.applyOptions(obj)
    }, [props.height, props.width]);

    const markers = useMemo(() => {
        return _.sortBy(
            Object.values(props.markers ?? {})
                .filter((l) => l.isVisible)
                .map(d => d.data).flat(),
            "time"
        )
    }, [props.markers]);
    useEffect(() => {
        if (markers) {
            mainSeriesRef.current?.setMarkers(markers)
        }
    }, [markers, props.markers]);

    const linesNew = useMemo(() => {
        return Object.entries(props.lines ?? {})
            .filter(([, l]) => l.isVisible)
            .map(([, l], idx) => {
                const chunkedLines = chunkByUndefined(l.data)
                if (chunkedLines.length === 1) {
                    return [<LineSeries
                        data={chunkedLines[0]}
                        initialData={chunkedLines[0]}
                        options={{
                            lineVisible: l.isVisible,
                            priceLineVisible: false,
                            lastValueVisible: false,
                            color: l.color ?? getColorFromSeed(idx)
                        }}
                    />]
                } else return chunkedLines.map(c =>
                    <LineSeries
                        data={c}
                        initialData={c}
                        options={{
                            lineVisible: l.isVisible,
                            priceLineVisible: false,
                            lastValueVisible: false,
                            color: l.color ?? getColorFromSeed(idx)
                        }}
                    />
                )
            }).flat()
    }, [props.lines]);

    return (
        <div key={props.key} className="h-full flex flex-col">
            <Chart
                key={props.key}
                ref={chartRef}
                className={`h-full flex`}
                options={DefaultChartOptions}>
                <CandleStickSeries key={`${props.key}:main`} ref={(r) => {
                    mainSeriesRef.current = r
                    mainSeriesRef.current?.setMarkers(markers)
                }} initialData={ohlcData} data={ohlcData}/>
                {/*{*/}
                {/*    linesNew*/}
                {/*}*/}
                {
                    linesData.map((d, idx) =>
                        <LineSeries
                            ref={(r) => {
                                if (r !== null){
                                    // mount TODO: Id
                                    linesRef.current[`line:${idx}`] = r;
                                } else {
                                    // unmount
                                    delete linesRef.current[`line:${idx}`];
                                }
                            }}
                            data={d.data}
                            initialData={d.data}
                            options={{
                                lineVisible: d.isVisible,
                                priceLineVisible: false,
                                lastValueVisible: false,
                            }}
                            />
                    )
                }
            </Chart>
        </div>

    )
})

function chunkByUndefined(arr: LineData[]): LineData[][] {
    const result: LineData[][] = [];
    let chunk: LineData[] = [];

    for (const item of arr) {
        if (item.value === undefined || item.value === null) {
            if (chunk.length > 0) {
                result.push(chunk);
                chunk = [];
            }
        } else {
            chunk.push(item);
        }
    }

    if (chunk.length > 0) {
        result.push(chunk);
    }
    return result;
}
