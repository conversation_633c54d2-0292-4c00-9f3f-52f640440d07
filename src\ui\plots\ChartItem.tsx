import {DeepPartial} from "lightweight-charts";
import {Markish} from "@observablehq/plot";
import * as Plot from "@observablehq/plot";
import _ from "lodash";
import {data} from "autoprefixer";

export type MarkOptions = {
    marker: {
        // can be constant, or a value
        value: number;
        direction: "x" | "y",
        label?: {
            suffix?: string
        } | true
    },
    line: {
        x: string,
        y: string,
        stroke: string,
        strokeWidth: number,
        crosshairMarker: boolean,
        crosshair: boolean
    },
    bar: Partial<{
        x: string,
        y: string,
        fill: string,
        stroke: string,
        crosshair: boolean
    }>,
    area: Partial<{
        x: string,
        x1: string,
        y1: string,
        x2: string,
        y2: string,
        fill: string,
        fillOpacity: number,
        crosshairMarker: boolean,
        crosshair: boolean

    }>,
    difference: {
        x1: string,
        y1: string,
        x2: string,
        y2: string,
        fill: string,
        stroke: string
    },
    scatter: {
        x: string,
        y: string
    }
}

const DefaultMarkOptions: MarkOptions = {
    line: {stroke: "blue", strokeWidth: 2, x: "x", y: "y", crosshairMarker: true, crosshair: true},
    area: {fill: "blue", fillOpacity: 0.8, x:undefined, x1: undefined, y1: undefined, x2: undefined, y2: undefined, crosshairMarker: true, crosshair: true},
    bar: {crosshair: true, fill: undefined, stroke: undefined, x: undefined, y: undefined}
}

export type MarkType = keyof MarkOptions

export type MarkItem<T extends MarkType = MarkType> = {
    type: T,
    options: DeepPartial<MarkOptions[T]> & { filter?: string },
    data?: any
}

function mergeDefaultOptions<T extends MarkType>(t: T, options: DeepPartial<MarkOptions[T]>): MarkOptions[T] {
    return _.mapValues(DefaultMarkOptions[t], (v, k) => {
        const key = k as keyof MarkOptions[T]
        if (options[key] === undefined)
            return v
        else
            return options[key]
    }) as MarkOptions[T]
}

export const MarkRenderFunctions: {[k in MarkType]: (markItem: MarkItem<k>, data: any) => Markish[]} = {
    area(markItem: MarkItem<"area">, data: any): Markish[] {
        const opts = mergeDefaultOptions("area", markItem.options)
        const dt = markItem.data ?? data
        const arr: Markish[] = [Plot.areaY(data, {...opts})];
        console.log("opts", opts)
        if (opts.crosshairMarker){
            arr.push(Plot.dot(dt, Plot.pointerX({x: markItem.options.x, y: markItem.options.y2, fill:markItem.options.fill})))
        }
        if (opts.crosshair) arr.push(Plot.crosshairX(dt, {x: opts.x, y: opts.y2}))
        return arr;
    }, bar(markItem: MarkItem<"bar">, data: any): Markish[] {
        const dt = markItem.data ?? data
        const arr: Markish[] = [Plot.barY(dt, {...markItem.options})];
        if (markItem.options.crosshair ?? DefaultMarkOptions.bar.crosshair){
            arr.push(Plot.crosshairX(dt, {x: markItem.options.x, y: markItem.options.y}))
        }
        return arr
    }, difference(markItem: MarkItem<"difference">, data: any): Markish[] {
        return [Plot.differenceY(data, {...markItem.options})];
    }, line(markItem: MarkItem<"line">, data: any): Markish[] {
        const dt = markItem.data ?? data
        const arr: Markish[] = [Plot.lineY(dt, {...markItem.options})]
        if (markItem.options.crosshairMarker ?? DefaultMarkOptions.line.crosshairMarker){
            arr.push(Plot.dot(dt, Plot.pointerX({x: markItem.options.x, y: markItem.options.y, fill:markItem.options.stroke})))
        }
        if (markItem.options.crosshair ?? DefaultMarkOptions.line.crosshair)
            arr.push(Plot.crosshairX(dt, {x: markItem.options.x, y: markItem.options.y}))
        return arr;
    }, marker(markItem: MarkItem<"marker">, data: any): Markish[] {
        if (markItem.options.direction === "y")
            return [
                Plot.ruleY([markItem.options.value], {...markItem.options}),
                // Plot.textY([markItem.options.value], { frameAnchor: "right", textAnchor:"start", text: (d) => `${d}\n(Avg. Loss)` })
            ];
        else
            return [Plot.ruleX([markItem.options.value], {...markItem.options})];
    }, scatter(markItem: MarkItem<"scatter">, data: any): Markish[] {
        return [Plot.dot(markItem.data ?? data, {...markItem.options})];
    }

}


// One Chart can have multiple series/marks
export type ChartItem = {
    marks: MarkItem[],
    data: Record<string, unknown>[],
    gridX?: boolean
    gridY?: boolean
    tickStepX?: number,
    x?: Plot.ScaleOptions
}