import {ChartElementTypeMap} from "./ChartElements.ts";
import {nanoid} from "nanoid";

// async
// keyed data store
// mutable keyed data store
// modifier store
// merged store

export interface DataStore<T> {
    getKey(item: T): string | number;

    data(): T[];

    getValue(key: string | number): T | undefined;

    subscribe(key: string, listener: (data: T[]) => void): void;

    subscribeUpdate(key: string, listener: (update: T) => void): void;

    unsubscribe(key: string): void;

    unsubscribeUpdate(key: string): void;

    first(): T | undefined;
}

export interface MutableDataStore<T> extends DataStore<T> {
    setData(list: T[]): void;

    update(data: T): void;
}

export class MergedStore<A, B, C> implements DataStore<C> {

    private _data: Record<string, C> = {};
    private readonly storeA: DataStore<A>;
    private readonly storeB: DataStore<B>;
    private readonly getKeyA: (value: A) => string | number;
    private readonly getKeyB: (value: B) => string | number;

    private readonly subscriptionId: string;

    private readonly merge: (a: A | undefined, b: B | undefined) => C | undefined;

    private subscribers: Record<string, (value: C[]) => void> = {};
    private updateSubscribers: Record<string, (value: C) => void> = {};

    constructor(storeA: DataStore<A>, storeB: DataStore<B>, merge: typeof this.merge, getKeyA: typeof this.getKeyA, getKeyB: typeof this.getKeyB) {
        this.storeA = storeA;
        this.storeB = storeB;
        this.getKeyA = getKeyA;
        this.getKeyB = getKeyB;
        this.merge = merge;

        this.subscriptionId = nanoid();

        this.storeA.subscribe(this.subscriptionId, (dt) => {
            dt.forEach(a => this.updateStoreAItem(a));
            this.emitAll();
        });
        this.storeB.subscribe(this.subscriptionId, (dt) => {
            dt.forEach(b => this.updateStoreBItem(b));
            this.emitAll();
        })
        this.storeA.subscribeUpdate(this.subscriptionId, (a) => {
            const updated = this.updateStoreAItem(a);
            if (updated !== undefined) {
                this.emitUpdate(updated);
            }
        })
        this.storeB.subscribeUpdate(this.subscriptionId, (b) => {
            const updated = this.updateStoreBItem(b);
            if (updated !== undefined) {
                this.emitUpdate(updated);
            }
        })
    }


    private updateStoreAItem(a: A): C | undefined {
        const key = this.getKeyA(a);
        const b = this.storeB.getValue(key);
        const newValue = this.merge(a, b);
        if (newValue !== undefined) {
            this._data[key] = newValue;
            return newValue;
        } else {
            delete this._data[key];
            return undefined;
        }
    }

    private updateStoreBItem(b: B) {
        const key = this.getKeyB(b);
        const a = this.storeA.getValue(key);
        const newValue = this.merge(a, b);
        if (newValue !== undefined) {
            this._data[key] = newValue;
            return newValue;
        } else {
            delete this._data[key];
            return undefined;
        }
    }

    private emitAll() {
        const dt = this.data();
        Object.values(this.subscribers).forEach(subscriber => {
            subscriber(dt);
        })
    }

    private emitUpdate(value: C) {
        Object.values(this.updateSubscribers).forEach(subscriber => {
            subscriber(value);
        })
    }


    data(): C[] {
        return Object.values(this._data);
    }

    subscribe(key: string, listener: (data: C[]) => void): void {
        this.subscribers[key] = listener;
        listener(this.data());
    }

    subscribeUpdate(key: string, listener: (update: C) => void): void {
        this.updateSubscribers[key] = listener;
    }

    unsubscribe(key: string): void {
        delete this.subscribers[key];
    }

    unsubscribeUpdate(key: string): void {
        delete this.subscribers[key];
    }

    getKey(): string | number {
        throw new Error('Method not supported in MergedStore yet.');
    }

    getValue(key: string | number): C | undefined {
        return this._data[key];
    }

    first(): C | undefined {
        for (const key in this._data) {
            return this._data[key];
        }
    }
}

// export class ModifierStore<T, R = T> implements DataStore<T> {
// // TODO: Make it keyed
//     private _data: T[] = [];
//     private _store: DataStore<R>;
//     private readonly modifier: (value: R) => T;
//     private readonly subscriptionId: string;
//
//     private listeners: Record<string, (data: T[]) => void> = {};
//     private updateListeners: Record<string, (data: T) => void> = {};
//
//     constructor(store: DataStore<R>, modifier: (value: R) => T) {
//         this._store = store;
//         this.modifier = modifier;
//         this.subscriptionId = nanoid();
//
//         // Subscribe to the underlying store
//         this._store.subscribe(this.subscriptionId, (data: R[]) => {
//             this._data = data.map(this.modifier);
//             Object.values(this.listeners).forEach(listener => {
//                 listener([...this._data]);
//             });
//         });
//
//         this._store.subscribeUpdate(this.subscriptionId, (update: R) => {
//             const modifiedUpdate = this.modifier(update);
//             this._data.push(modifiedUpdate);
//             Object.values(this.updateListeners).forEach(listener => {
//                 listener(modifiedUpdate);
//             });
//         });
//     }
//
//     data(): T[] {
//         return [...this._data];
//     }
//
//     subscribe(key: string, listener: (data: T[]) => void): void {
//         this.listeners[key] = listener;
//         // Immediately call with current modified data
//         listener([...this._data]);
//     }
//
//     subscribeUpdate(key: string, listener: (update: T) => void): void {
//         this.updateListeners[key] = listener;
//     }
//
//     unsubscribe(key: string): void {
//         delete this.listeners[key];
//     }
//
//     unsubscribeUpdate(key: string): void {
//         delete this.updateListeners[key];
//     }
//
//     // Clean up when no longer needed
//     destroy(): void {
//         this._store.unsubscribe(this.subscriptionId);
//         this._store.unsubscribeUpdate(this.subscriptionId);
//     }
// }

// export class ArrayDataStore<T> implements DataStore<T> {
//
//     private _data: T[] = [];
//
//     private listeners: Record<string, (data: T[]) => void>= {};
//     private updateListeners: Record<string, (data: T) => void> = {};
//
//     data(): T[] {
//         return [...this._data];
//     }
//
//     setData(list: T[]): void {
//         this._data.push(...list);
//         Object.values(this.listeners).forEach((listener) => {
//             listener(Object.values(this._data));
//         })
//     }
//
//     update(data: T): void {
//         this._data.push(data);
//         Object.values(this.updateListeners).forEach((listener) => {
//             listener(data);
//         })
//     }
//
//     subscribe(key: string, listener: (data: T[]) => void): void {
//         this.listeners[key] = listener;
//         listener([...this._data]);
//     }
//
//     subscribeUpdate(key: string, listener: (update: T) => void): void {
//         this.updateListeners[key] = listener;
//     }
//
//     unsubscribe(key: string): void {
//         delete this.listeners[key];
//     }
//
//     unsubscribeUpdate(key: string): void {
//         delete this.updateListeners[key];
//     }
// }

export class TimeSeriesStore<T> implements MutableDataStore<T> {
    getTime: (item: T) => number | string;
    getKey: (item: T) => number | string;
    private _data: Record<number | string, T> = {};
    private _first: { key: string | number, value: T } | undefined = undefined;

    getValue(key: string | number): T | undefined {
        return this._data[key];
    }

    subscribers: Record<string, (data: T[]) => void> = {};
    updateSubscribers: Record<string, (data: T) => void> = {};

    constructor(getTime: (item: T) => number | string) {
        this.getTime = getTime;
        this.getKey = getTime;
    }

    subscribe(key: string, listener: (data: T[]) => void) {
        this.subscribers[key] = listener;
        listener(Object.values(this._data));
    }

    unsubscribe(key: string) {
        delete this.subscribers[key];
    }

    subscribeUpdate(key: string, listener: (update: T) => void) {
        this.updateSubscribers[key] = listener;
    }

    unsubscribeUpdate(key: string) {
        delete this.updateSubscribers[key];
    }

    setData(list: T[]) {
        list.forEach((item: T) => {
            const key = this.getTime(item);
            this._data[key] = item;
            if (this._first === undefined) {
                this._first = { key: key, value: item };
            } else {
                if (this._first.key > key){
                    this._first = { key: key, value: item };
                }
            }
        })
        const dt = this.data();
        Object.values(this.subscribers).forEach((listener) => {
            listener(dt);
        })
    }

    update(update: T) {
        const key = this.getTime(update);
        this._data[key] = update;
        Object.values(this.updateSubscribers).forEach((listener) => {
            listener(update);
        })
    }

    data(): T[] {
        return Object.values(this._data);
    }

    first(): T | undefined {
        return this._first?.value;
    }
}

type ChartElements = "line" | "ohlc" | "marker" | "fill"

// TODO: Support for multiline
export type ChartElementsStoreOld = {
    [key in ChartElements]: Record<string, TimeSeriesStore<ChartElementTypeMap[key][number]>>
}