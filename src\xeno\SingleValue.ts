import {StudyType, StudyTypeValue} from "../types/study/StudyType.ts";
import {DbBlock} from "../types/study/db/DbBlock.ts";
import {StudyParam} from "../types/study/ReadStudyParam.ts";
import {BlockTypeMap, SingleValueType, Value} from "./Block.ts";

export interface SingleValue<T extends SingleValueType = SingleValueType> extends Value<T> {
    offset?: number
}

export type SeriesElement = BlockTypeMap["Series"]

const SeriesValueProps: Record<SeriesElement, { valueType: StudyType }> = {
    Close: {valueType: "Number"},
    High: {valueType: "Number"},
    Low: {valueType: "Number"},
    Open: {valueType: "Number"}
}
export class SeriesValue implements SingleValue<"Series"> {
    type = "Series" as const;
    value: SeriesElement;
    offset?: number;

    constructor(value: SeriesElement, offset?: number) {
        this.value = value;
        this.offset = offset;
    }

    getValueType(): StudyType {
        return SeriesValueProps[this.value].valueType;
    }

    getString(): string {
        return this.value + (this.offset ? `-${this.offset}` : "")
    }

    detailedString(): string {
        return "TODO"; // use prev.
    }

    toDb(): DbBlock<"Series"> {
        return {
            type: "Series",
            value: this.value,
            offset: this.offset
        };
    }

    static fromDb(dbBlock: DbBlock<"Series">): SeriesValue {
        // @ts-expect-error resolve later - harmless
        return new SeriesValue(dbBlock.value, dbBlock.offset)
    }
}

export class StudyValue implements SingleValue<"Study"> {
    type = "Study" as const;
    value: StudyParam;
    offset?: number;

    constructor(value: StudyParam, offset?: number) {
        this.value = value;
        this.offset = offset;
    }

    getValueType(): StudyType {
        return this.value.valueType;
    }

    getString(): string {
        return this.value.paramName + (this.offset ? `-${this.offset}` : "")
    }

    detailedString(): string {
        return "TODO";
    }

    toDb(): DbBlock<"Study"> {
        return {
            type: "Study",
            value: this.value,
            offset: this.offset
        };
    }

    static fromDb(dbBlock: DbBlock<"Study">): StudyValue {
        return new StudyValue(dbBlock.value, dbBlock.offset)
    }
}

export class ConstantValue implements SingleValue<"Constant"> {
    type = "Constant" as const;
    offset?: number = undefined;
    value: StudyTypeValue;

    constructor(value: StudyTypeValue) {
        this.value = value;
    }

    getValueType(): StudyType {
        return this.value.type;
    }

    getString(): string {
        return String(this.value.value) // TODO: implement study type value to string
    }

    detailedString(): string {
        return "TODO";
    }

    toDb(): DbBlock<"Constant"> {
        return {
            type: "Constant",
            value: this.value
        };
    }

    static fromDb(dbBlock: DbBlock<"Constant">): ConstantValue {
        return new ConstantValue(dbBlock.value)
    }
}