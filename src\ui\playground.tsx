import useContainerSize from "../utils/hooks/useContainerSize.ts";
import {ReactNode, useEffect, useMemo, useState} from "react";
import {Badge, Segmented, Tooltip} from "antd";
import {
    ArrowsUpDownIcon as ArrowsUpDownIconOutlined,
    ChartPieIcon as ChartPieIconOutlined,
    CommandLineIcon as CommandLineIconOutlined,
    PaintBrushIcon as PaintBrushIconOutlined,
} from "@heroicons/react/24/outline";
import {IconToggle} from "../components/IconToggle.tsx";
import {ClipboardDocumentListIcon as ClipboardDocumentListIconOutlined} from "@heroicons/react/24/outline";
import {Cog6ToothIcon, CommandLineIcon, PaintBrushIcon} from "@heroicons/react/24/solid";
import {ArrowsUpDownIcon, ChartPieIcon, ClipboardDocumentListIcon} from "@heroicons/react/24/solid";
import {TCData} from "../data/StrategyTerminal.ts";
import {
    ChartElementDataItem,
    ChartElementHeaderWithStore,
} from "../utils/ChartElementsParser.ts";
import {ChartLine, Filter, Plus, Search, Sparkles, X} from "lucide-react";
import {Broker, Brokers} from "../data/brokers/Broker.ts";
import {SelectBroker} from "../data/brokers/SelectBroker.tsx";
import InstrumentSelectorModal from "../components/SymbolSelector.tsx";
import {DefaultInstrument, queryInstruments} from "../data/Instruments.ts";
import TimeframeSelector from "../components/TimeframeSelector.tsx";
import {Timeframe} from "../types/study/Config.ts";
import {Style, TCControl, TerminalInput, useTerminalData} from "../data/Terminal.ts";
import {TVChart} from "../chart/TVChart.tsx";
import {DataStore} from "../utils/Store.ts";
import {SingleExpression, ExpressionBuilder} from "../components/condition/ConditionBuilder.tsx";
import EONTabContent from "./Eon.tsx";
import {StyleComponent} from "../components/StyleComponent.tsx";


export function Playground() {

    const [broker, setBroker] = useState<Broker>(Brokers.Fyers);
    const tcData = useTerminalData(broker.id);

    return (
        <>
            <PlaygroundTerminal TCData={tcData} broker={broker} setBroker={setBroker}/>
        </>
    )
}


/*
 * TODO UI: Collapsible Tabs
 * */
export function PlaygroundTerminal(props: {
    TCData: TCData & TCControl,
    broker: Broker,
    setBroker: (b: Broker) => void
}) {

    const [instrument, setInstrument] = useState(DefaultInstrument);
    const [timeframe, setTimeframe] = useState<Timeframe>({multiplier: 15, unit: "m"});
    const [expressions, setExpressions] = useState<SingleExpression[]>([]);
    const [styles, setStyles] = useState<Style[]>([]);

    useEffect(() => {
        setExpressions(props.TCData.expressions);
    }, [props.TCData.expressions]);

    useEffect(() => {
        setStyles(props.TCData.styles)
    }, [props.TCData.styles]);

    useEffect(() => {
        const terminalInput: TerminalInput = {
            expressions: expressions,
            timeSeriesList: [{
                instrumentId: instrument.id,
                timeframe: Timeframe.toString(timeframe)
            }],
            styles: styles
        }
        props.TCData.sendTerminalMessage(terminalInput);
    }, [timeframe, instrument]);


    const stores: ChartElementHeaderWithStore[] = useMemo(() => {
        return [...(props.TCData.actionElementHeaders ?? []), ...(props.TCData.studyElementHeaders ?? [])];
    }, [props.TCData.actionElementHeaders, props.TCData.studyElementHeaders]);

    const ohlcStore: DataStore<ChartElementDataItem["ohlc"]> | undefined = useMemo(() => {
        // Find by current selected instrument / timeframe
        return stores.find(it => it.type === "ohlc")?.store as DataStore<ChartElementDataItem["ohlc"]> | undefined
    }, [stores]);

    const [activeTabs, setActiveTabs] = useState<Set<"trades" | "positions" | "orders" | "logs" | "eon">>(new Set());
    const {ref, size} = useContainerSize<HTMLDivElement>();
    const [selectedTradeType, setSelectedTradeType] = useState<"Active" | "Completed">("Active");
    const [leftActiveTabs, setLeftActiveTabs] = useState<Set<"studies" | "styles">>(new Set());

    const handleExpressionAdded = (expression: SingleExpression, s: Style[]) => {
        const terminalInput: TerminalInput = {
            expressions: [...expressions, expression],
            timeSeriesList: [{
                instrumentId: instrument.id,
                timeframe: Timeframe.toString(timeframe)
            }],
            styles: [...styles, ...s]
        }
        props.TCData.sendTerminalMessage(terminalInput);
    }

    return (
        <div className={"h-screen max-h-screen flex overflow-y-hidden overflow-x-hidden"}>
            {/*  Sidebar  */}
            <div className="w-0 bg-[#fafafc] border-r-2">

            </div>

            <div className={"flex-1 min-h-0 flex flex-col w-full max-h-screen"}>
                {/* Header */}
                <div className="h-12 bg-[#fafafc] border-b-2">
                    <div className={"w-full h-full px-4 py-2 flex items-center space-x-2"}>
                        <h1 className={"text-zinc-700 font-semibold text-lg"}>Terminal</h1>
                        <div className="flex-1 min-h-0"></div>
                        <SelectBroker brokerId={props.broker} setSelectedBroker={props.setBroker}/>
                    </div>
                </div>
                <div className={`bg-[#f3f4fb] flex-1 min-h-0 flex`}>
                    {/* Chart Container */}
                    <div className={"flex-[1] border-r-2 border-b-2 flex flex-col"}>
                        <div className={"flex-1  min-h-0 flex"}>
                            <div className={"flex-1 min-h-0"}>
                                <div className={"flex h-full "}>
                                    <div className="w-12 bg-[#fafafc] border-r-2 items-center flex flex-col">
                                        <TabsToggleList activeTabs={leftActiveTabs} setActiveTabs={setLeftActiveTabs}
                                                        counts={{}} items={LeftToggleItems}/>
                                    </div>
                                    <div className={`w-60 bg-[#fafafc] border-r-2`}
                                         style={{
                                             display: leftActiveTabs.has("studies") ? "block" : "none",
                                             overflowY: "auto"
                                         }}>
                                        <div className="p-3 bg-white border-b border-gray-200 sticky top-0 z-10">
                                            <div className="flex items-center gap-2">
                                                <h3 className="text-sm font-semibold text-gray-800">Studies</h3>
                                                <span
                                                    className="ml-auto text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                                    {expressions.length}
                                                </span>
                                            </div>
                                        </div>

                                        {/* Conditions List */}
                                        <div className="p-2 space-y-2">
                                            {expressions.map((cond) => (
                                                <div className="group">
                                                    <div
                                                        className="bg-white border border-gray-200 rounded-md p-3 hover:shadow-sm transition-shadow">
                                                        <div className="flex items-start justify-between mb-2">
                                                        <span
                                                            className="text-sm font-medium text-gray-800 leading-tight">
                                                            {cond.name}
                                                        </span>
                                                            <button
                                                                onClick={() => {
                                                                    setExpressions(expressions.filter(c => c.name !== cond.name))
                                                                }}
                                                                className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 transition-all ml-2"
                                                            >
                                                                <X size={14}/>
                                                            </button>
                                                        </div>
                                                        <div
                                                            className="text-xs text-gray-600 bg-gray-50 p-2 rounded border font-mono leading-relaxed">
                                                            {cond.expression.map((item, index) => {
                                                                if (item.type === "Series" || item.type === "Study" || item.type === "Constant") {
                                                                    return (
                                                                        <span key={index}
                                                                              className="text-blue-600 font-medium">
                                                                            {item.getString()}
                                                                        </span>
                                                                    );
                                                                } else if (item.type === "ComparisonOperator" || item.type === "BooleanOperator" || item.type === "ArithmeticOperator") {
                                                                    return (
                                                                        <span key={index}
                                                                              className="text-purple-600 mx-1">
                                                                            {item.getString()}
                                                                        </span>
                                                                    );
                                                                } else return (
                                                                    <span key={index}
                                                                          className="text-gray-600 mx-1">
                                                                            {item.getString()}
                                                                        </span>
                                                                );
                                                            }).filter(Boolean).map((element, index) => (
                                                                <span key={index}>
                                                                    {index > 0 && ' '}
                                                                    {element}
                                                                </span>
                                                            ))}
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}

                                            {/* Add Condition Button */}
                                            <div className="p-3">
                                                <ExpressionBuilder onExpressionAdded={handleExpressionAdded}>
                                                    <button
                                                        className="w-full flex items-center justify-center gap-2 px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                                                        <Plus size={16}/>
                                                        Add Condition
                                                    </button>
                                                </ExpressionBuilder>
                                            </div>

                                            {/* Empty State */}
                                            {expressions.length === 0 && (
                                                <div className="p-6 text-center">
                                                    <div className="text-gray-400 mb-2">
                                                        <Filter size={32} className="mx-auto mb-2"/>
                                                    </div>
                                                    <p className="text-sm text-gray-500">No conditions defined</p>
                                                    <p className="text-xs text-gray-400 mt-1">Add your first condition
                                                        to
                                                        get started</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                    <div className={`w-60 bg-[#fafafc] border-r-2`}
                                         style={{
                                             display: leftActiveTabs.has("styles") ? "block" : "none",
                                             overflowY: "auto"
                                         }}>
                                        {
                                            styles.map(s => {
                                                const h = stores.find(store => store.name === s.name);
                                                return (
                                                    <StyleComponent
                                                        style={s}
                                                        isVisible={h?.optionsStore.value().isVisible ?? false}
                                                        onToggled={(b) => {
                                                            h?.optionsStore.updateFn(p => ({...p, isVisible: b})).then();
                                                            setStyles(styles.map(style => style.name === s.name ? { ...style, isVisible: b } : style))
                                                        }}
                                                        onSave={(s) => {
                                                            h?.optionsStore.updateFn(p => ({...p, ...(s.options)})).then();
                                                            // @ts-expect-error resolve later
                                                            setStyles(styles.map(style => style.name === s.name ? { ...style, options: s.options } : style))
                                                        }}
                                                    />
                                                )
                                            })
                                        }
                                    </div>
                                    <div className={"flex-1 flex flex-col"}>
                                        <div className="h-10 border-b-2 bg-[#fafafc] flex divide-x-2">
                                            <InstrumentSelectorModal
                                                defaultQuery={instrument.symbol}
                                                onSelect={setInstrument}
                                                fetchInstruments={(query, _, selected) => queryInstruments(props.broker.id, query, selected)}>
                                                <Tooltip title={instrument.symbol} placement={"bottom"}
                                                         mouseEnterDelay={1}>
                                                    <button
                                                        className="px-2 py-0.5 w-40 flex space-x-2 items-center hover:bg-gray-200 cursor-pointer truncate">
                                                        <Search className={"w-4"}/>
                                                        <span className={"flex-1 truncate text-start"}>
                                                        {instrument.symbol}
                                                        </span>
                                                    </button>
                                                </Tooltip>
                                            </InstrumentSelectorModal>
                                            <TimeframeSelector
                                                onTimeframeSelected={setTimeframe}>
                                                <Tooltip title={instrument.symbol} placement={"bottom"}
                                                         mouseEnterDelay={1}>
                                                    <button
                                                        className="px-2 py-0.5 min-w-12 flex space-x-2 items-center hover:bg-gray-200 cursor-pointer truncate">
                                                        <span className={"flex-1 truncate text-center"}>
                                                        {Timeframe.toString(timeframe)}
                                                        </span>
                                                    </button>
                                                </Tooltip>
                                            </TimeframeSelector>
                                            <div className="border-r-2"></div>
                                        </div>
                                        <div className="flex-1 relative" ref={ref}>
                                            <TVChart ohlcStore={ohlcStore} key={"chartKey"} stores={stores}
                                                     containerKey={"main"}
                                                     width={size.width}
                                                     height={size.height}/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {/*{*/}
                            {/*    view === "stats" &&*/}
                            {/*    <StatsView/>*/}
                            {/*}*/}
                        </div>
                    </div>


                    {
                        // Logs Container
                        activeTabs.has("logs") &&

                        <div className={"bg-[#fafafc] w-60 flex flex-col min-h-0 border-r-2"}>
                            <div className={"pl-3 pr-1 py-1 border-b flex items-center"}>
                                <h1 className={"text-zinc-700 font-semibold "}>Logs</h1>
                                <div className="flex-1 min-h-0"></div>
                                <div className={"p-2 text-zinc-700 cursor-pointer rounded-full hover:bg-zinc-700/20"}>
                                    <Cog6ToothIcon className={"w-4 h-4"}/>
                                </div>
                            </div>
                            <div className={"flex-1 min-h-0 overflow-y-scroll"}>
                                {/*<LogViewer logs={logs ?? []} theme={"light"} lastVisited={l => {*/}
                                {/*    lastVisitedLogTime.current = l.time*/}
                                {/*}}/>*/}
                            </div>

                        </div>
                    }
                    {
                        // Logs Container
                        activeTabs.has("eon") &&

                        <div className={"bg-[#fafafc] w-80 flex flex-col min-h-0 border-r-2"}>
                            <EONTabContent onSendMessage={(prompt) => {
                                const terminalInput: TerminalInput = {
                                    expressions: expressions,
                                    timeSeriesList: [{
                                        instrumentId: instrument.id,
                                        timeframe: Timeframe.toString(timeframe)
                                    }],
                                    styles: []
                                }
                                props.TCData.sendEonTerminalMessage(prompt, terminalInput);
                            }}/>
                        </div>
                    }


                    {
                        // Logs Container
                        (activeTabs.has("positions") || activeTabs.has("orders")) &&

                        <div className={"bg-[#fafafc] w-60 flex flex-col min-h-0 border-r-2"}>
                            {
                                activeTabs.has("positions") &&
                                <>
                                    <div className={"pl-3 pr-1 py-1 border-b flex items-center"}>
                                        <h1 className={"text-zinc-700 font-semibold "}>Positions</h1>
                                        <div className="flex-1 min-h-0"></div>
                                        <div
                                            className={"p-2 text-zinc-700 cursor-pointer rounded-full hover:bg-zinc-700/20"}>
                                            <Cog6ToothIcon className={"w-4 h-4"}/>
                                        </div>
                                    </div>
                                    <div className={"flex-1 min-h-0 overflow-y-scroll"}>
                                    </div>
                                </>
                            }
                            {
                                activeTabs.has("orders") &&
                                <>
                                    <div className={"pl-3 pr-1 py-1 border-y flex items-center"}>
                                        <h1 className={"text-zinc-700 font-semibold "}>Orders</h1>
                                        <div className="flex-1 min-h-0"></div>
                                        <div
                                            className={"p-2 text-zinc-700 cursor-pointer rounded-full hover:bg-zinc-700/20"}>
                                            <Cog6ToothIcon className={"w-4 h-4"}/>
                                        </div>
                                    </div>
                                    <div className={"flex-1 min-h-0 overflow-y-scroll"}>
                                    </div>
                                </>
                            }
                        </div>
                    }

                    {/* Strategy/Trades Container */}
                    {
                        activeTabs.has("trades") &&

                        <div className={"bg-[#fafafc] w-60 flex flex-col min-h-0"}>
                            <div className={"pl-3 pr-1 py-1 border-b flex items-center"}>
                                <h1 className={"text-zinc-700 font-semibold "}>Trades</h1>
                                <div className="flex-1 min-h-0"></div>
                                <div className={"p-2 text-zinc-700 cursor-pointer rounded-full hover:bg-zinc-700/20"}>
                                    <Cog6ToothIcon className={"w-4 h-4"}/>
                                </div>
                            </div>
                            <div className={"px-3 py-2 border-b"}>
                                <Segmented<typeof selectedTradeType>
                                    value={selectedTradeType}
                                    options={['Active', 'Completed']}
                                    onChange={setSelectedTradeType}
                                />
                            </div>
                            <div className={"flex-1 p-2 min-h-0 overflow-y-auto"}>
                                <div className={""}>
                                    {/*{*/}
                                    {/*    (selectedTradeType === "Active" ? activeTrades : completedTrades).map(trade => (*/}
                                    {/*        <TradeComp trade={trade} key={trade.tradeInstanceId}*/}
                                    {/*                   allowActions={selectedTradeType === "Active"}*/}
                                    {/*                   onClose={() => clientHandle?.closeTrade(trade.tradeInstanceId)}*/}
                                    {/*                   pnl={tradesPnl[trade.tradeInstanceId]}/>*/}
                                    {/*    ))*/}
                                    {/*}*/}
                                </div>
                            </div>

                        </div>
                    }

                    {/* Right Side Bar */}
                    <div className={"w-12 bg-[#fafafc] border-l-2"}>
                        <TabsToggleList activeTabs={activeTabs} setActiveTabs={setActiveTabs}
                                        counts={{logs: 0}} items={RightToggleItems}/>
                    </div>
                </div>
            </div>
        </div>
    )
        ;
}

const LeftToggleItems: IconToggleItem<"studies" | "styles">[] = [
    {
        id: 'studies',
        label: 'Studies',
        icon: <ChartLine className={"w-5"}/>,
        iconToggled: <ChartLine className={"w-5"}/>
    },
    {id: 'styles', label: 'Styles', icon: <PaintBrushIconOutlined/>, iconToggled: <PaintBrushIcon/>},
]

const RightToggleItems: IconToggleItem<"trades" | "positions" | "orders" | "logs" | "eon">[] = [
    {id: 'trades', label: 'Trades', icon: <ArrowsUpDownIconOutlined/>, iconToggled: <ArrowsUpDownIcon/>},
    {id: 'positions', label: 'Positions', icon: <ChartPieIconOutlined/>, iconToggled: <ChartPieIcon/>},
    {
        id: 'orders',
        label: 'Orders',
        icon: <ClipboardDocumentListIconOutlined/>,
        iconToggled: <ClipboardDocumentListIcon/>
    },
    {id: 'logs', label: 'Logs', icon: <CommandLineIconOutlined/>, iconToggled: <CommandLineIcon/>},
    {
        id: 'eon',
        label: 'Eon',
        icon: <Sparkles className={"w-5 pb-0.5"}/>,
        iconToggled: <Sparkles className={"w-5 pb-0.5"}/>
    }
]

type IconToggleItem<T extends string> = {
    id: T,
    label: string,
    icon: ReactNode,
    iconToggled: ReactNode
}
type IconToggleListProps<T extends string> = {
    activeTabs: Set<T>,
    setActiveTabs: (activeTabs: Set<T>) => void,
    counts: Partial<Record<T, number>>,
    items?: IconToggleItem<T>[]
}

function TabsToggleList<T extends string>({activeTabs, setActiveTabs, counts, items}: IconToggleListProps<T>) {


    const handleToggle = (id: T) => {
        const newSet = new Set(activeTabs);
        if (newSet.has(id)) {
            newSet.delete(id);
        } else {
            newSet.add(id);
        }
        setActiveTabs(newSet);
    };

    return (
        <div className="max-w-xs mx-auto py-4">
            <div className="flex flex-col items-center space-y-3">
                {items?.map((button) => (
                    // @ts-expect-error later
                    <Badge offset={[-6]} size={"small"} count={counts[button.id]}>
                        <IconToggle
                            key={button.id}
                            id={button.id}
                            label={button.label}
                            icon={button.icon}
                            iconToggled={button.iconToggled}
                            isActive={activeTabs.has(button.id /*as "trades" | "positions" | "orders" | "logs"*/)}
                            // @ts-expect-error solve later
                            onToggle={handleToggle}
                        />
                    </Badge>
                ))}
            </div>
        </div>
    );
}