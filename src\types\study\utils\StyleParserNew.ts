import {Style} from "../Style";
import {checkConfigurableType} from "../../configurables/ConfigurableValue";
import {StudyTypePrimitive} from "../StudyType";


// TODO: Create a new parser, that precomputes the functions to parse the style.
export class StyleParser {

    // getParser(style: Style){
    //     const constants: Record<string, any> = {}
    //     const branches = []
    //     Object.entries(style.configurableOpts).forEach(([key, v]) => {
    //         if (checkConfigurableType("Constant", v.value)){
    //             constants[key] = v.value
    //         }
    //         else if (checkConfigurableType("Study", v.value)){
    //             branches.push((item: StudyTypePrimitive): [string, any] => {
    //                 // TODO: Parse
    //                 return
    //             })
    //         }
    //     })
    // }

}