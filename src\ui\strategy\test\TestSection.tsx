import {Tabs} from "antd";
import {useMemo} from "react";
import {useBacktestApi} from "../../../data/BacktestOld.ts";
import {useMainSelector} from "../../../redux/MainStore.ts";
import {OverallTab} from "./Overall.tsx";
import {StatsTab} from "./StatsTab.tsx";
import {TradesTab} from "./TradesTab.tsx";

// TODO: Create transformers -> takes type, takes id of chart element, a function that modifies its data
// selection transformer will change color of every other data that is not selected

export function TestSection(/*props: TestSectionProps*/) {
    const strategy = useMainSelector((state) => state.strategy);
    const backtestApiResponse = useBacktestApi(strategy);

    const tabItems = useMemo(() => {
        return [
            {
                label: `Overall`,
                key: "Overall",
                children: <OverallTab backtestApiResponse={backtestApiResponse} />,
                closable: false
            },
            {
                label: `Stats`,
                key: "Stats",
                children: <StatsTab backtestApiResponse={backtestApiResponse} />,
                closable: false
            },
            {
                label: `Trades`,
                key: "Trades",
                children: <TradesTab backtestApiResponse={backtestApiResponse} />,
                closable: false
            },
            {
                label: `Orders`,
                key: "Orders",
                children: <div className={"p-4"}>
                    Sections - Pnl Curve, Pnl distribution, Trades
                    <br/>
                    <s>Pnl, Drawdown - By Trade #</s>
                    <br/>
                    Pnl, Drawdown - By time (should have a candlestick series with dropdown to select a series)
                    <br/>
                    Realized vs Unrealized Pnl
                    <br/>
                    Scatter plot
                    <br/>
                    <s>winning trades, losing trades, with average</s> and highest markers
                    <br/>
                    <s>profitable trades / losing trades</s>
                    <br/>
                    winrate, sharpe, profit factor, expectancy, volatility
                    <br/>
                    pnl distribution by token, by target, by action (bar)
                    <br/>
                    <s>pie chart for number of winning trades/losing trades</s>
                    <br/>
                    <s>profit range vs. number of trades</s>
                    <br/>
                    in winning trades and losing trades, can show stacked peak unrealized pnl for the trade
                    <br/><br/><br/>
                    chart (chart element controls, symbol, timeframe, adding studies), strategy controls (can add strategy, list of action instances, add manual entry, tp, sl's)
                </div>,
                closable: false
            },
        ]
    }, [backtestApiResponse]);

    return (
        <div className={"w-full h-full"}>
            <Tabs className={"h-full overflow-y-auto"} type={"card"} size={"small"} items={tabItems}/>

            {/*<OverallTab backtestApiResponse={backtestApiResponse}/>*/}
        </div>
    )
}