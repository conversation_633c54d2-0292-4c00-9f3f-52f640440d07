import {Action} from "./strategy/Actions.ts";
import {ValueOrPercent} from "./ValueOrPercent.ts";
import {Quantity} from "./strategy/OrderDetails.ts";
import {Dayjs} from "dayjs";

export type Strategy = {
    name: string,
    actions: Action[],
    riskConfig: RiskConfig,
    settings: Settings
}

export type RiskConfig = {
    maxDrawdown?: ValueOrPercent,
    maxLoss?: number,
    allowTradesBetween: {
        from?: Dayjs,
        to?: Dayjs
    },
    intradaySquareOff?: Dayjs,
}

export type Settings = {
    backtestRange: {
        from: Dayjs,
        to?: Dayjs
    },
    maxQuantity?: Quantity
}