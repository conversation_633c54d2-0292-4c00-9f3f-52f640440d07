import React from 'react'
import { ResponsiveScatterPlot, ScatterPlotDatum } from '@nivo/scatterplot'

// Define the shape of a single data point
interface DataPoint {
    x: number
    y: number
    id?: string
    size?: number
}

// Define the shape of a data series
interface Series {
    id: string
    data: DataPoint[]
    color?: string
}

// Define component props
interface ScatterPlotProps {
    data: Series[]
    xAxis?: {
        label?: string
        min?: number
        max?: number
    }
    yAxis?: {
        label?: string
        min?: number
        max?: number
    }
    width?: number
    height?: number
    colors?: string[] | ((series: Series) => string)
    pointSize?: number | ((point: DataPoint) => number)
    margin?: { top: number; right: number; bottom: number; left: number }
    enableGridX?: boolean
    enableGridY?: boolean
    axisBottomLegend?: string
    axisLeftLegend?: string
}

const ScatterPlotChart: React.FC<ScatterPlotProps> = ({
                                                          data,
                                                          xAxis = {},
                                                          yAxis = {},
                                                          width = 900,
                                                          height = 500,
                                                          colors = { scheme: 'nivo' },
                                                          pointSize = 10,
                                                          margin = { top: 40, right: 40, bottom: 70, left: 70 },
                                                          enableGridX = true,
                                                          enableGridY = true,
                                                          axisBottomLegend = '',
                                                          axisLeftLegend = '',
                                                      }) => {
    const Layers = ({ innerWidth, innerHeight, xScale, yScale }: any) => {
        const y0 = yScale(0)
        return (
            <line
                x1={0}
                y1={y0}
                x2={innerWidth}
                y2={y0}
                stroke="#888"
                strokeWidth={1.5}
                strokeDasharray="5,5"
            />
        )
    }
    return (
        <div style={{ height, width }}>
            <ResponsiveScatterPlot
                data={data}
                margin={margin}
                layers={[
                    'grid',
                    'axes',
                    'nodes',
                    'markers',
                    'mesh',
                    'legends',
                    Layers // Add our custom layer with the horizontal line
                ]}
                xScale={{
                    type: 'linear',
                    min: xAxis.min === undefined ? 'auto' : xAxis.min,
                    max: xAxis.max === undefined ? 'auto' : xAxis.max,
                }}
                yScale={{
                    type: 'linear',
                    min: yAxis.min === undefined ? 'auto' : yAxis.min,
                    max: yAxis.max === undefined ? 'auto' : yAxis.max,
                }}
                colors={"rgb(244, 117, 96)"}
                // colors={colors}
                // pointSize={pointSize}
                // pointColor={{ theme: 'background' }}
                // pointBorderWidth={2}
                // pointBorderColor={{ from: 'serieColor' }}
                enableGridX={enableGridX}
                enableGridY={enableGridY}
                axisTop={null}
                axisRight={null}
                axisBottom={{
                    tickSize: 5,
                    tickPadding: 5,
                    tickRotation: 0,
                    legend: axisBottomLegend || xAxis.label || 'X',
                    legendPosition: 'middle',
                    legendOffset: 46,
                }}
                axisLeft={{
                    tickSize: 5,
                    tickPadding: 5,
                    tickRotation: 0,
                    legend: axisLeftLegend || yAxis.label || 'Y',
                    legendPosition: 'middle',
                    legendOffset: -50,
                }}
                legends={[
                    {
                        anchor: 'bottom-right',
                        direction: 'column',
                        justify: false,
                        translateX: 0,
                        translateY: 0,
                        itemWidth: 100,
                        itemHeight: 12,
                        itemsSpacing: 5,
                        itemDirection: 'left-to-right',
                        symbolSize: 12,
                        symbolShape: 'circle',
                        effects: [
                            {
                                on: 'hover',
                                style: {
                                    itemOpacity: 1,
                                },
                            },
                        ],
                    },
                ]}
            />
        </div>
    )
}

export default ScatterPlotChart