import * as ScrollArea from '@radix-ui/react-scroll-area';
import { MessageCircle, Send, Sparkles } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';

interface Message {
    id: number;
    text: string;
    isUser: boolean;
}

interface EONTabContentProps {
    onSendMessage: (message: string) => void;
}

const EONTabContent: React.FC<EONTabContentProps> = ({ onSendMessage }) => {
    const [messages, setMessages] = useState<Message[]>([
        {
            id: 1,
            text: "Hello! I'm <PERSON><PERSON>, your AI assistant. How can I help you today?",
            isUser: false,
        },
    ]);
    const [message, setMessage] = useState('');
    const scrollAreaRef = useRef<HTMLDivElement>(null);

    const handleSend = () => {
        if (message.trim()) {
            const newMessage: Message = {
                id: messages.length + 1,
                text: message,
                isUser: true,
            };
            setMessages([...messages, newMessage]);
            onSendMessage(message); // Call the prop callback with the message
            setMessage('');
        }
    };

    // Auto-scroll to bottom when new messages are added
    useEffect(() => {
        if (scrollAreaRef.current) {
            scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
        }
    }, [messages]);

    return (
        <div className="flex flex-col h-full bg-gray-100 text-gray-900 p-4">
            {/* Header */}
            <div className="flex items-center gap-2 mb-4">
                <Sparkles className="w-6 h-6 text-blue-500" />
                <h2 className="text-lg font-semibold">EON Assistant</h2>
            </div>

            {/* Chat Area */}
            <ScrollArea.Root className="flex-1 mb-4">
                <ScrollArea.Viewport
                    className="w-full h-[calc(100vh-200px)] pr-4"
                    ref={scrollAreaRef}
                >
                    <div className="space-y-4">
                        {messages.map((msg) => (
                            <div
                                key={msg.id}
                                className={`flex ${msg.isUser ? 'justify-end' : 'justify-start'}`}
                            >
                                <div
                                    className={`rounded-lg p-3 max-w-[80%] text-sm ${
                                        msg.isUser ? 'bg-blue-500 text-white' : 'bg-white shadow-sm border border-gray-200'
                                    }`}
                                >
                                    {msg.text}
                                </div>
                            </div>
                        ))}
                    </div>
                </ScrollArea.Viewport>
                <ScrollArea.Scrollbar
                    className="w-2 bg-gray-200 rounded-full"
                    orientation="vertical"
                >
                    <ScrollArea.Thumb className="bg-gray-400 rounded-full" />
                </ScrollArea.Scrollbar>
            </ScrollArea.Root>

            {/* Input Area */}
            <div className="flex items-center gap-2">
        <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Ask EON anything..."
            className="flex-1 bg-white rounded-lg p-3 text-sm resize-none h-12 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSend();
                }
            }}
        />
                <button
                    onClick={handleSend}
                    className="p-3 bg-blue-500 rounded-lg hover:bg-blue-600 text-white transition-colors disabled:opacity-50"
                    disabled={!message.trim()}
                >
                    <Send className="w-5 h-5" />
                </button>
            </div>
        </div>
    );
};

export default EONTabContent;