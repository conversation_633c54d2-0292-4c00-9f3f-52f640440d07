import {ChartElements, ChartElementType} from "./ChartElements.ts";
import _ from "lodash";

export type ChartElementTransformer<T extends ChartElementType> = {
    elementType: T,
    which: string[],
    transformData: (obj: ChartElements[T][string]) => ChartElements[T][string]
}

// TODO: Proper API - Chaining or Builder, Filter group
export function mergeChartElements(e1: ChartElements, e2: ChartElements): ChartElements {
    return {
        line: {
            ...e1.line,
            ...e2.line
        },
        marker: {
            ...e1.marker,
            ...e2.marker
        },
        ohlc: {
            ...e1.ohlc,
            ...e2.ohlc
        },
        multiline: {
            ...e1.multiline,
            ...e2.multiline
        },
        fill: {
            ...e1.fill,
            ...e2.fill
        }
    }
}


export function transformChartElementsByType<T extends ChartElementType>(elements: ChartElements, transform: ChartElementTransformer<T>): ChartElements {
    return {
        ...elements,
        [transform.elementType]: _.mapValues(elements[transform.elementType], (v, k) => {
            if (transform.which.includes(k)) {
                return transform.transformData(v)
            } else return v
        })
    }
}

export function transformChartElements<T extends ChartElementType>(elements: ChartElements, transformer: (obj: ChartElements[T][string], key: string) => ChartElements[T][string]): ChartElements {
    return _.mapValues(elements, (v1) => {
        return _.mapValues(v1, (v, k) => {
            return transformer(v, k)
        })
    }) as ChartElements
}


export function excludeChartElementsByGroup(elements: ChartElements, group: string[]): ChartElements {
    return _.mapValues(elements, (v1) => {
        return _.pickBy(v1, (v, k) => {
            return !group.includes(v.group)
        })
    }) as ChartElements
}