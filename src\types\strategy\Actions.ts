import {ExitTarget, Target} from "./Target.ts";
import {Id, newId} from "../../utils/Id.ts";

type ActionMap = {
    "Entry" : {
        condition: string | null, // TODO: From study
        targets: Target[]
    },
    "Exit": {
        forEntry: Id,
        condition: string | null, // TODO: From study
        targets: ExitTarget[]
    }
}

export type ActionType = keyof ActionMap
export type Action<T extends keyof ActionMap = keyof ActionMap> = {
    _id: Id,
    type: T,
    /** Unique name */
    name: string,
} & ActionMap[T]

export type Entry = Action<"Entry">
export type Exit = Action<"Exit">

export const Constructors = {
    entry: (name: string): Entry => ({ _id: newId(), type:"Entry", name, condition: null,targets:[]}),
    exit: (name: string, forEntry: Id): Exit => ({ _id: newId(), type:"Exit", name, forEntry, condition: null,targets:[]})
}