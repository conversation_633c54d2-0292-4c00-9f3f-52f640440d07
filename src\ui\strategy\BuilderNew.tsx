import React, { useState } from 'react';
import {
    DesktopOutlined,
    FileOutlined,
    PieChartOutlined,
    TeamOutlined,
    UserOutlined,
} from '@ant-design/icons';
import {MenuProps, Typography} from 'antd';
import {  Layout, Menu, theme } from 'antd';
import {StrategySection} from "./StrategySection.tsx";
import {RiskSection} from "./Risk.tsx";
import {SettingsSection} from "./Settings.tsx";
import {TestSection} from "./test/TestSection.tsx";
import { Stats } from './Stats.tsx';

const { Header, Content, Sider } = Layout;

// region navigation
type MenuItem = Required<MenuProps>['items'][number];

function getItem(
    label: React.ReactNode,
    key: React.Key,
    icon?: React.ReactNode,
    children?: MenuItem[],
): MenuItem {
    return {
        key,
        icon,
        children,
        label,
    } as MenuItem;
}

const items: MenuItem[] = [
    getItem('Option 1', '1', <PieChartOutlined />),
    getItem('Option 2', '2', <DesktopOutlined />),
    getItem('User', 'sub1', <UserOutlined />, [
        getItem('Tom', '3'),
        getItem('Bill', '4'),
        getItem('Alex', '5'),
    ]),
    getItem('Team', 'sub2', <TeamOutlined />, [getItem('Team 1', '6'), getItem('Team 2', '8')]),
    getItem('Files', '9', <FileOutlined />),
];
// endregion

export const BuilderNew: React.FC = () => {
    const [collapsed, setCollapsed] = useState(true);
    const [currentItem, setCurrentItem] = useState("Strategy")
    const {
        token: { colorBgContainer, borderRadiusLG },
    } = theme.useToken();

    return (
        <Layout style={{ minHeight: '100vh' }}>
            {/*<Sider collapsible collapsed={collapsed} onCollapse={(value) => setCollapsed(value)}>*/}
            {/*    <div className="demo-logo-vertical" />*/}
            {/*    <Menu theme="dark" defaultSelectedKeys={['1']} mode="inline" items={items} />*/}
            {/*</Sider>*/}
            <Layout style={{height: '100vh'}}>
                <Header style={{ padding: 0, background: colorBgContainer }} >
                    <Menu onClick={(i) => { setCurrentItem(i.key) }} selectedKeys={[currentItem]} mode="horizontal" items={ConfigTabItems} />;

                </Header>
                <Content style={{ background:"#FCFCFC", height:"100%" }}>
                    <div
                        className={ `${currentItem !== "Test" && currentItem !== "Strategy" ? "p-6" :""} h-full`}
                        style={{
                            minHeight: "100%",
                            height: "100%",
                            // backgroundColor: "#000",
                            // background: colorBgContainer,
                            borderRadius: borderRadiusLG,
                        }}
                    >
                        {
                            currentItem === "Strategy" &&
                            <StrategySection/>
                        }
                        {
                            currentItem === "Risk" &&
                            <RiskSection/>
                        }
                        {
                            currentItem === "Settings" &&
                            <SettingsSection/>
                        }
                        {
                            currentItem === "Test" &&
                            <TestSection/>
                        }
                        {
                            currentItem === "Stats" &&
                            <Stats />
                        }
                    </div>
                </Content>
                {/*
                <Footer style={{ textAlign: 'center' }}>
                    Ant Design ©{new Date().getFullYear()} Created by Ant UED
                </Footer>*/}
            </Layout>
        </Layout>
    );
};

const ConfigTabItems: MenuProps['items'] = [
    {
        key: 'Strategy',
        label: 'Strategy',
    },
    {
        key: 'Risk',
        label: 'Risk',
    },
    {
        key: 'Settings',
        label: 'Settings',
    },
    {
        key: "Test",
        label: <Typography.Text style={{color: "#1677ff"}}>Test</Typography.Text>
    },
    {
        key: 'Stats',
        label: 'Stats',
    },
];