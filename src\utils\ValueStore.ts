type Subscriber<T> = (value: T) => void;
// type Unsubscribe = () => void;

export interface ValueStore<T> {
    value() : T
    subscribe(key: string, subscriber: Subscriber<T>): void
    unsubscribe(key: string): void
}

export class MutableValueStore<T> implements ValueStore<T> {

    private data: T;
    private subscribers: Record<string, Subscriber<T>> = {};

    constructor(initialValue: T) {
        this.data = initialValue;
    }

    private async emitAsync(value: T) {
        (Object.values(this.subscribers)).forEach(subscriber => {
            subscriber(value);
        })
    }

    private async emitToAsync(subscriber: Subscriber<T>, value: T) {
        subscriber(value);
    }

    subscribe(key: string, subscriber: Subscriber<T>): void {
        this.subscribers[key] = subscriber;
        this.emitToAsync(subscriber, this.data);
    }

    unsubscribe(key: string): void {
        delete this.subscribers[key];
    }

    update(newValue: T): void {
        this.data = newValue;
        this.emitAsync(newValue);
    }

    async updateFn(fn: (prev: T) => T) {
        const newValue = fn(this.value());
        this.data = newValue;
        await this.emitAsync(newValue);
    }

    value(): T {
        return this.data;
    }
}