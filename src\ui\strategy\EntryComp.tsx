import {useState} from "react";
import {TargetEditor, TargetEditorProps} from "../../components/TargetEditor.tsx";
import {Constructors, Target} from "../../types/strategy/Target.ts";
import {<PERSON><PERSON>, Card, Space, Tag, Typography} from "antd";
import {DeleteOutlined, PlusOutlined} from "@ant-design/icons";
import {AutosizeInputWithEditIcon} from "../../components/AutosizeInputWithEditIcon.tsx";
import {StudyParamSelector} from "../../components/StudyParamSelector.tsx";
import {TargetTable} from "./Targets.tsx";
import {Entry} from "../../types/strategy/Actions.ts";


type EntryCompProps = {
    entry: Entry,
    onChange: (entry: Entry) => void,
    onRemove: () => void
}

export function EntryComp(props: EntryCompProps) {

    const [modalProps, setModalProps] = useState<TargetEditorProps | null>(null)

    const addNewTarget = () => {
        setModalProps({
            isModalOpen: true,
            initialTarget: Constructors.Target(),
            onCancel(): void {
                setModalProps(null)
            },
            onOk(target: Target): void {
                setModalProps(null)
                props.onChange({
                    ...props.entry,
                    targets: [...props.entry.targets, target]
                })
            }
        })
    }
    const modifyTarget = (t: Target, idx: number) => {
        setModalProps({
            isModalOpen: true,
            initialTarget: t,
            onCancel(): void {
                setModalProps(null)
            },
            onOk(target: Target): void {
                setModalProps(null)
                const newTargets = [...props.entry.targets]
                newTargets[idx] = target
                props.onChange({
                    ...props.entry,
                    targets: newTargets
                })
            }
        })
    }

    return (
        <>
            <Card size="small"
                  extra={
                      <Button onClick={props.onRemove} icon={<DeleteOutlined/>} shape={"circle"} type={"text"} color={"danger"} variant={"text"}></Button>
                  }
                  title={(
                      <div className={"flex items-center "}>
                          <Tag color={"blue"}>Entry</Tag>
                          <AutosizeInputWithEditIcon value={props.entry.name} onChange={(name) => props.onChange({...props.entry, name})}/>
                      </div>
                  )}
                  style={{width: "100%", minHeight: "320px"}}>
                <Space direction={"vertical"} style={{width: "100%"}}>

                    <Space>
                        <Typography.Text type={"secondary"}>Entry Condition</Typography.Text>

                        <StudyParamSelector/>
                    </Space>

                    <div className="h-1"></div>
                    <Typography.Text type={"secondary"}>Targets</Typography.Text>
                    <TargetTable targets={props.entry?.targets ?? []} onTargetClicked={modifyTarget}/>
                    <div className={""}></div>
                    <Button icon={<PlusOutlined/>} type={"link"} size={"small"} onClick={addNewTarget}>Add Target</Button>
                    {
                        modalProps && <TargetEditor {...modalProps}/>
                    }
                </Space>
            </Card>
        </>
    )
}
