import {Space, Table, TableProps, Tag, Tooltip, Typography} from "antd";
import {Target} from "../../types/strategy/Target.ts";
import {OrderType, OrderValidity} from "../../types/strategy/OrderDetails.ts";

type TargetTableProps = {
    targets: Target[],
    onTargetClicked: (target: Target, idx: number) => void
}

export function TargetTable(props: TargetTableProps) {
    return (
        <Table
            bordered={true}
            onRow={(t, idx) => ({
                onClick: () => {
                    if (idx !== undefined)
                        props.onTargetClicked(t, idx)
                }
            })}
            size={"small"}
            columns={TargetTableColumns}
            dataSource={props.targets}
            showHeader={false}
            pagination={false}
        />
    )
}

export const TargetTableColumns: TableProps<Target>['columns'] = [
    {
        title: 'Side',
        dataIndex: 'side',
        key: 'side',
        // render: (_, {side}) => <Tag color={side === "B" ? "#108ee9" : "#f50"}>
        render: (_, {orderDetails}) => (
            // <Space.Compact>
                <Tag style={{margin: 0}} color={orderDetails.side === "Buy" ? "green" : "orange"}>
                    {orderDetails.side}
                </Tag>
                // <Tag>{Instrument.getShortDisplayName(instrument)}</Tag>
            // </Space.Compact>
        )
    },
    {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        align:"start",
        render: (_, {ticker}) => (
            <Typography.Text>
                {ticker.displayName}
            </Typography.Text>
        )
    },
    {
        title: 'Qty',
        dataIndex: 'qty',
        key: 'qty',
        render: (_, {orderDetails}) => (
            <Typography.Text type={"secondary"}>
                {`${orderDetails.quantity.value}${orderDetails.quantity.type === "Lots" ? " Lots" : ""}`}
            </Typography.Text>
        )
    },
    {
        title: "Order Type",
        dataIndex: 'orderType',
        key: "orderType",
        align: "center",
        render: (_, {orderDetails}) => (
            <Space.Compact>
                <Tooltip title={OrderType.getDisplayName(orderDetails.orderType)}>
                    <Tag>{OrderType.getShortDisplayName(orderDetails.orderType)}</Tag>
                </Tooltip>
                <Tooltip title={OrderValidity.getDisplayName(orderDetails.validity)}>
                    <Tag>{orderDetails.validity}</Tag>
                </Tooltip>
            </Space.Compact>
        )
    },
];