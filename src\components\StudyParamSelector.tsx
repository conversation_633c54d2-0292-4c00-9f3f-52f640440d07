import {AutoComplete, AutoCompleteProps, Select} from "antd";
import {DownOutlined} from "@ant-design/icons";
import {useRef, useState} from "react";

const TempOptions: AutoCompleteProps["options"] = [
    {label: "len", value: "len"},
    {label: "ema", value: "ema"},
    {label: "buy", value: "buy"}
]

type StudyParamSelectorProps = {}

// TODO: Remove invalid input
export function StudyParamSelector(props: StudyParamSelectorProps) {
    const [options] = useState(TempOptions)

    return (
        <>
            <Select
                options={options}
                showSearch
                style={{minWidth: 200}}
                placeholder="Select from study"
            />
        </>
    )
}