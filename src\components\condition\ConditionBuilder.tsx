import { ExpressionEditor } from "../../xeno/ExpressionEditor.tsx";
import { Block } from "../../xeno/Block.ts";
import { ReactNode, useState } from "react";
import { StudyType } from "../../types/study/StudyType.ts";
import { BlockUtils } from "../../xeno/Utils.ts";
import * as Dialog from "@radix-ui/react-dialog";
import {Style} from "../../data/Terminal.ts";

export type SingleExpression = {
    name: string;
    expression: Block[];
    valueType: StudyType;
    // style: ExpressionStyle;
};


type ExpressionModalProps = {
    children: ReactNode;
    onExpressionAdded: (expression: SingleExpression, styles: Style[]) => void;
}

export function ExpressionBuilder(props: ExpressionModalProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [expressionName, setExpressionName] = useState("");
    const [expressionBlocks, setExpressionBlocks] = useState<Block[]>([]);
    const [errors, setErrors] = useState<{ name?: string; expression?: string }>({});
    const [prevStudyType, setPrevStudyType] = useState<StudyType | undefined>(undefined);

    const handleOpenChange = (open: boolean) => {
        setIsOpen(open);
        if (!open) {
            resetForm();
        }
    };

    const resetForm = () => {
        setExpressionName("");
        setExpressionBlocks([]);
        setErrors({});
        // setStyle({ type: 'Line', lineColor: '#000000' });
        // setBooleanStyleType('Marker');
        setPrevStudyType(undefined);
    };

    const validateExpression = (blocks: Block[]) => {
        if (!blocks || blocks.length === 0) {
            return "Expression is required";
        }
        const result = BlockUtils.calculateOutType(blocks);
        return result.isSuccess ? undefined : "Invalid Expression";
    };

    const getStudyType = (blocks: Block[]) => {
        if (!blocks || blocks.length === 0) return undefined;
        const result = BlockUtils.calculateOutType(blocks);
        return result.isSuccess ? result.value : undefined;
    };

    const validateForm = () => {
        const newErrors: { name?: string; expression?: string } = {};

        if (!expressionName.trim()) {
            newErrors.name = "Expression name is required";
        }

        const expressionError = validateExpression(expressionBlocks);
        if (expressionError) {
            newErrors.expression = expressionError;
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleAddExpression = () => {
        if (!validateForm()) {
            return;
        }

        const result = BlockUtils.calculateOutType(expressionBlocks);
        if (!result.isSuccess) {
            setErrors(prev => ({ ...prev, expression: "Invalid Expression" }));
            return;
        }

        const newExpression: SingleExpression = {
            name: expressionName.trim(),
            expression: expressionBlocks,
            valueType: result.value,
            // style: style,
        };

        const styles: Style[] = []
        if (newExpression.valueType === "Number"){
            styles.push({
                name: newExpression.name,
                type: "line",
                key: {
                    studyDepId: newExpression.name,
                    paramName: newExpression.name,
                    valueType: newExpression.valueType
                },
                options: {
                    lineColor: "#f49f54",
                    lineOpacity: 0.8,
                }
            })
        } else if (newExpression.valueType === 'Boolean'){
            styles.push({
                name: newExpression.name,
                type: "marker",
                key: {
                    studyDepId: newExpression.name,
                    paramName: newExpression.name,
                    valueType: newExpression.valueType
                },
                options: {

                }
            })
        }

        props.onExpressionAdded(newExpression, styles);
        setIsOpen(false);
        resetForm();
    };

    const handleExpressionChange = (blocks: Block[]) => {
        setExpressionBlocks(blocks);
        const expressionError = validateExpression(blocks);
        setErrors(prev => ({ ...prev, expression: expressionError }));

        const studyType = getStudyType(blocks);
        if (studyType !== prevStudyType) {
            // Only reset style if StudyType changes
            // if (studyType === 'Number') {
            //     setStyle({ type: 'Line', lineColor: '#000000' });
            // } else if (studyType === 'Boolean') {
            //     setStyle({ type: 'Marker', color: '#000000', shape: 'circle', position: 'aboveBar' });
            //     setBooleanStyleType('Marker');
            // }
            setPrevStudyType(studyType);
        }
    };

    const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setExpressionName(e.target.value);
        if (errors.name) {
            setErrors(prev => ({ ...prev, name: undefined }));
        }
    };

    // const [lineDialogOpen, setLineDialogOpen] = useState(false);
    // const [markerDialogOpen, setMarkerDialogOpen] = useState(false);
    // const [highlightDialogOpen, setHighlightDialogOpen] = useState(false);

    // const renderStyleOptions = () => {
    //     const studyType = getStudyType(expressionBlocks);
    //     if (!studyType) return null;
    //
    //     if (studyType === 'Number') {
    //         return (
    //             <div className="space-y-3">
    //                 <h4 className="text-sm font-medium text-gray-700">Style Options</h4>
    //                 <LineStyleDialog
    //                     isOpen={lineDialogOpen}
    //                     onOpenChange={setLineDialogOpen} style={style as LineStyle} onChange={handleStyleChange} />
    //             </div>
    //         );
    //     } else if (studyType === 'Boolean') {
    //         return (
    //             <div className="space-y-3">
    //                 <h4 className="text-sm font-medium text-gray-700">Style Options</h4>
    //                 <div className="space-y-3">
    //                     <div className="space-y-2">
    //                         <label className="block text-xs font-medium text-gray-600">Style Type</label>
    //                         <select
    //                             value={booleanStyleType}
    //                             onChange={(e: ChangeEvent<HTMLSelectElement>) => {
    //                                 const newType = e.target.value as 'Marker' | 'Highlight';
    //                                 setBooleanStyleType(newType);
    //                                 setStyle(
    //                                     newType === 'Marker'
    //                                         ? { type: 'Marker', color: style.type === 'Marker' ? style.color : '#000000', shape: style.type === 'Marker' ? style.shape : 'circle', position: style.type === 'Marker' ? style.position : 'aboveBar' }
    //                                         : { type: 'Highlight', highlightColor: style.type === 'Highlight' ? style.highlightColor : '#000000', highlightOpacity: style.type === 'Highlight' ? style.highlightOpacity : 0.5 }
    //                                 );
    //                             }}
    //                             className="w-full px-3 py-1.5 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
    //                         >
    //                             <option value="Marker">Marker</option>
    //                             <option value="Highlight">Highlight</option>
    //                         </select>
    //                     </div>
    //                     {booleanStyleType === 'Marker' ? (
    //                         <MarkerStyleDialog
    //                             isOpen={markerDialogOpen}
    //                             onOpenChange={setMarkerDialogOpen} style={style as MarkerStyle} onChange={handleStyleChange} />
    //                     ) : (
    //                         <HighlightStyleDialog
    //                             isOpen={highlightDialogOpen}
    //                             onOpenChange={setHighlightDialogOpen} style={style as HighlightStyle} onChange={handleStyleChange} />
    //                     )}
    //                 </div>
    //             </div>
    //         );
    //     }
    //     return null;
    // };

    const getExpressionTypeDisplay = () => {
        const studyType = getStudyType(expressionBlocks);
        if (!studyType) return (
            <p id="expression-error" className="text-xs text-red-600">{errors.expression ?? ""}</p>
        )

        return (
            <span className={`inline-flex items-center text-xs font-medium   text-gray-500 '}`}>
                {studyType === "Boolean" ? "Condition" : studyType}
            </span>
        );
    };

    // const studyType = getStudyType(expressionBlocks);
    // const hasStyleOptions = studyType === 'Number' || studyType === 'Boolean';

    return (
        <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
            <Dialog.Trigger asChild>
                {props.children}
            </Dialog.Trigger>
            <Dialog.Portal>
                <Dialog.Overlay className="fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0">
                    <Dialog.Content className="fixed flex flex-col left-[50%] min-w-[400px] min-h-[400px] overflow-auto top-[50%] z-50 w-full max-w-2xl max-h-[80vh] translate-x-[-50%] translate-y-[-50%] rounded-lg bg-white shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]">
                        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                            <div>
                                <Dialog.Title className="text-lg font-semibold text-gray-900">
                                    Create New Expression
                                </Dialog.Title>
                                {/*<Dialog.Description className="text-sm text-gray-500 mt-1">*/}
                                {/*    Define an expression with a name and expression*/}
                                {/*</Dialog.Description>*/}
                            </div>
                            <Dialog.Close asChild>
                                <button className="rounded-full p-2 hover:bg-gray-100 transition-colors" aria-label="Close dialog">
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </Dialog.Close>
                        </div>
                        <div className="flex-1 p-6 space-y-5">
                            <div className="space-y-2">
                                <label htmlFor="expression-name" className="block text-sm font-medium text-gray-700">
                                    Expression Name
                                </label>
                                <input
                                    id="expression-name"
                                    type="text"
                                    value={expressionName}
                                    onChange={handleNameChange}
                                    placeholder="Enter expression name"
                                    className={`w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
                                    aria-invalid={!!errors.name}
                                    aria-describedby={errors.name ? "expression-name-error" : undefined}
                                />
                                {errors.name && (
                                    <p id="expression-name-error" className="text-sm text-red-600">{errors.name}</p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <div className="flex items-baseline h-6 space-x-3">
                                    <label className="block text-sm font-medium text-gray-700">
                                        Expression
                                    </label>
                                    {getExpressionTypeDisplay()}
                                </div>
                                <ExpressionEditor
                                    placeholder="Enter expression"
                                    onChange={handleExpressionChange}
                                    width="100%"
                                    aria-invalid={!!errors.expression}
                                    aria-describedby={errors.expression ? "expression-error" : undefined}
                                />
                            </div>
                        </div>
                        <div className="flex-1"></div>
                        <div className="flex items-center justify-end p-4 border-t border-gray-200 bg-gray-50">
                            <button
                                type="button"
                                onClick={handleAddExpression}
                                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={!expressionName.trim() || !expressionBlocks.length || !!validateExpression(expressionBlocks)}
                            >
                                Add Expression
                            </button>
                        </div>
                    </Dialog.Content>
                </Dialog.Overlay>
            </Dialog.Portal>
        </Dialog.Root>
    );
}