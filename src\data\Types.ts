

export type PositionData = {
    name: string,
    price: number | null,
    quantity: number
}

type ActionDataTypeMap = {
    entry: object,
    exit: object,
    sl: {
        price: number | null,
    },
    close: object
}

export type ActionType = keyof ActionDataTypeMap

export type ActionData = {
    actionId: string,
    actionInstanceId: string,
    state: "not triggered" | "active" | "completed",
    type: ActionType,
    positions: PositionData[],
}

export type TradeData = {
    isActive: boolean,
    isOpen: boolean,
    tradeId: string,
    tradeInstanceId: string,
    actions: ActionData[]
}
