import React, { useState, useEffect, useCallback, useRef } from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import * as Dialog from '@radix-ui/react-dialog';
import { Search, X, TrendingUp, BarChart3, ArrowUpDown, ArrowDownUp, Building, Loader } from 'lucide-react';
import { Instrument } from '../data/Instruments';

// Props interface
interface InstrumentSelectorModalProps {
    defaultQuery?: string;
    onSelect: (instrument: Instrument) => void;
    fetchInstruments: (query: string, page: number, selectedTypes: Instrument['type'][]) => Promise<Instrument[]>;
    children: React.ReactNode;
}

const typeFilters = [
    { type: 'EQ' as Instrument['type'], label: 'Stock', icon: TrendingUp },
    { type: 'FUT' as Instrument['type'], label: 'Future', icon: BarChart3 },
    { type: 'CE' as Instrument['type'], label: 'Call', icon: ArrowUpDown },
    { type: 'PE' as Instrument['type'], label: 'Put', icon: ArrowDownUp },
    { type: 'INDEX' as Instrument['type'], label: 'Index', icon: Building },
];

// Debounce hook
const useDebounce = (value: string, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);

    return debouncedValue;
};

// TODO: Infinite scroll support
const InstrumentSelectorModal: React.FC<InstrumentSelectorModalProps> = ({
                                                                             defaultQuery = '',
                                                                             onSelect,
                                                                             fetchInstruments,
                                                                             children
                                                                         }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [query, setQuery] = useState(defaultQuery);
    const [selectedTypes, setSelectedTypes] = useState<Instrument['type'][]>([]);
    const [instruments, setInstruments] = useState<Instrument[]>([]);
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [page, setPage] = useState(0);
    const [error, setError] = useState<string | null>(null);
    const [initialLoad, setInitialLoad] = useState(false);
    const [highlightedIndex, setHighlightedIndex] = useState(-1);

    const scrollContainerRef = useRef<HTMLDivElement>(null);
    const itemRefs = useRef<(HTMLDivElement | null)[]>([]);

    const debouncedQuery = useDebounce(query, 300);

    // Reset state when modal opens
    const handleOpenChange = (open: boolean) => {
        setIsOpen(open);
        if (open) {
            setQuery(defaultQuery);
            setSelectedTypes([]);
            setInstruments([]);
            setPage(0);
            setHasMore(true);
            setError(null);
            setInitialLoad(false);
            setHighlightedIndex(-1);
        }
    };

    // Fetch instruments function - Remove `loading` from dependencies
    const loadInstruments = useCallback(async (searchQuery: string, pageNum: number, types: Instrument['type'][], append = false) => {
        setLoading(true);
        setError(null);

        try {
            const newInstruments = await fetchInstruments(searchQuery, pageNum, types);

            if (append) {
                setInstruments(prev => [...prev, ...newInstruments]);
            } else {
                setInstruments(newInstruments);
                setHighlightedIndex(-1); // Reset highlighted index on new search
            }

            // If we get fewer instruments than expected, assume no more
            setHasMore(false);
            setPage(pageNum);
        } catch (err) {
            setError('Failed to load instruments');
            setHasMore(false);
            console.error('Error loading instruments:', err);
        } finally {
            setLoading(false);
        }
    }, [fetchInstruments]); // Remove `loading` from dependencies

    // Load more instruments for infinite scroll
    const loadMore = () => {
        if (hasMore && !loading) {
            loadInstruments(debouncedQuery, page + 1, selectedTypes, true);
        }
    };

    // Initial load effect - only run once when modal opens
    useEffect(() => {
        if (isOpen && !initialLoad) {
            setInitialLoad(true);
            loadInstruments("nifty", 0, selectedTypes, false);
        }
    }, [isOpen, initialLoad, loadInstruments, selectedTypes]);

    // Search and filter effect - run when query or filters change (after initial load)
    useEffect(() => {
        if (isOpen && initialLoad) {
            console.log("rerun");
            // Reset for new search
            setInstruments([]);
            setPage(0);
            setHasMore(true);
            loadInstruments(debouncedQuery, 0, selectedTypes, false);
        }
    }, [debouncedQuery, selectedTypes, isOpen, initialLoad, loadInstruments]);

    // Scroll highlighted item into view
    const scrollToItem = (index: number) => {
        if (itemRefs.current[index] && scrollContainerRef.current) {
            const item = itemRefs.current[index];
            const container = scrollContainerRef.current;

            const itemRect = item.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();

            if (itemRect.top < containerRect.top) {
                container.scrollTop -= containerRect.top - itemRect.top;
            } else if (itemRect.bottom > containerRect.bottom) {
                container.scrollTop += itemRect.bottom - containerRect.bottom;
            }
        }
    };

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (instruments.length === 0) return;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                const nextIndex = highlightedIndex < instruments.length - 1 ? highlightedIndex + 1 : 0;
                setHighlightedIndex(nextIndex);
                scrollToItem(nextIndex);
                break;

            case 'ArrowUp':
                e.preventDefault();
                const prevIndex = highlightedIndex > 0 ? highlightedIndex - 1 : instruments.length - 1;
                setHighlightedIndex(prevIndex);
                scrollToItem(prevIndex);
                break;

            case 'Enter':
                e.preventDefault();
                if (highlightedIndex >= 0 && highlightedIndex < instruments.length) {
                    handleSelect(instruments[highlightedIndex]);
                }
                break;

            case 'Escape':
                e.preventDefault();
                setIsOpen(false);
                break;
        }
    };

    // Toggle type filter
    const toggleType = (type: Instrument['type']) => {
        setSelectedTypes(prev =>
            prev.includes(type)
                ? prev.filter(t => t !== type)
                : [...prev, type]
        );
    };

    // Handle instrument selection
    const handleSelect = (instrument: Instrument) => {
        onSelect(instrument);
        setIsOpen(false);
    };

    return (
        <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
            {/* Trigger Button */}
            <Dialog.Trigger asChild>
                {children}
            </Dialog.Trigger>

            {/* Modal Portal */}
            <Dialog.Portal>
                <Dialog.Overlay className="fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />

                <Dialog.Content
                    className="fixed left-[50%] top-[50%] z-50 w-full max-w-2xl max-h-[80vh] translate-x-[-50%] translate-y-[-50%] overflow-hidden rounded-lg bg-white shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]"
                    onKeyDown={handleKeyDown}
                >
                    {/* Header */}
                    <div className="flex items-center justify-between p-4 border-b">
                        <Dialog.Title className="text-lg font-semibold">
                            Select Instrument
                        </Dialog.Title>
                        <Dialog.Close asChild>
                            <button className="p-1 hover:bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-gray-500">
                                <X size={20} />
                            </button>
                        </Dialog.Close>
                    </div>

                    {/* Search Bar */}
                    <div className="p-4 border-b">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                            <input
                                type="text"
                                value={query}
                                onChange={(e) => setQuery(e.target.value)}
                                placeholder="Search instruments..."
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                onKeyDown={(e) => {
                                    if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                                        e.preventDefault();
                                        handleKeyDown(e);
                                    }
                                }}
                            />
                        </div>
                    </div>

                    {/* Type Filters */}
                    <div className="p-3 border-b">
                        <div className="flex flex-wrap gap-2">
                            {typeFilters.map(({ type, label, icon: Icon }) => (
                                <button
                                    key={type}
                                    onClick={() => toggleType(type)}
                                    className={`flex items-center gap-1.5 px-2.5 py-1 text-xs rounded-full border transition-colors ${
                                        selectedTypes.includes(type)
                                            ? 'bg-blue-100 border-blue-300 text-blue-700'
                                            : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                                    }`}
                                >
                                    <Icon size={14} />
                                    {label}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Instruments List */}
                    <div className="flex-1 overflow-hidden">
                        <div
                            ref={scrollContainerRef}
                            className="h-96 overflow-y-auto"
                        >
                            {error && (
                                <div className="p-4 text-red-600 bg-red-50 border-b">
                                    {error}
                                </div>
                            )}

                            {instruments.length === 0 && !loading && !error && initialLoad && (
                                <div className="p-8 text-center text-gray-500">
                                    No instruments found
                                </div>
                            )}

                            <InfiniteScroll
                                pageStart={0}
                                loadMore={loadMore}
                                hasMore={hasMore && !loading}
                                loader={
                                    <div key="loader" className="p-4 flex items-center justify-center">
                                        <Loader className="animate-spin mr-2" size={20} />
                                        Loading instruments...
                                    </div>
                                }
                                useWindow={false}
                                getScrollParent={() => scrollContainerRef.current}
                                className="instrument-scroll-container"
                            >
                                {instruments.map((instrument, index) => (
                                    <div
                                        key={`${instrument.symbol}-${instrument.brokerId}-${index}`}
                                        ref={(el) => itemRefs.current[index] = el}
                                        onClick={() => handleSelect(instrument)}
                                        onMouseEnter={() => setHighlightedIndex(index)}
                                        className={`px-3 py-2 border-b cursor-pointer focus:outline-none transition-colors ${
                                            highlightedIndex === index
                                                ? 'bg-blue-50 border-blue-200'
                                                : 'hover:bg-gray-50'
                                        }`}
                                        tabIndex={0}
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter' || e.key === ' ') {
                                                e.preventDefault();
                                                handleSelect(instrument);
                                            }
                                        }}
                                    >
                                        <div className="flex items-center gap-4">
                                            <span className="font-medium text-gray-900 text-sm w-40 truncate flex-shrink-0">
                                                {instrument.symbol}
                                            </span>
                                            <span className="text-sm text-gray-600 flex-1 truncate">
                                                {instrument.name}
                                            </span>
                                            <span className="px-1.5 py-0.5 text-xs bg-gray-100 text-gray-600 rounded uppercase flex-shrink-0">
                                                {instrument.type}
                                            </span>
                                        </div>
                                    </div>
                                ))}
                            </InfiniteScroll>
                        </div>
                    </div>
                </Dialog.Content>
            </Dialog.Portal>
        </Dialog.Root>
    );
};

export default InstrumentSelectorModal;