import {
    BitmapCoordinatesR<PERSON>ing<PERSON>cope,
    CanvasRenderingTarget2D,
} from 'fancy-canvas';
import {
    CustomData,
    ICustomSeriesPaneRenderer,
    PriceToCoordinateConverter,
    Time,
    LineData,
} from 'lightweight-charts';
import {
    CustomSeriesOptions,
    customSeriesDefaultOptions,
} from 'lightweight-charts';
import {
    CustomSeriesPricePlotValues,
    ICustomSeriesPaneView,
    PaneRendererCustomData,
    WhitespaceData,
} from 'lightweight-charts';

export interface MultilineData extends CustomData {
    lines: Record<string, LineData>;
}

interface ProcessedLinePoint {
    x: number;
    y: number;
    originalData: LineData;
}

export interface MultilineSeriesOptions extends CustomSeriesOptions {
    lineWidth: number;
    pointMarkersVisible: boolean;
    pointMarkersRadius?: number;
    getLineColor: (lineId: string, lineData: LineData) => string;
}

export const defaultMultilineOptions: MultilineSeriesOptions = {
    ...customSeriesDefaultOptions,
    lineWidth: 2,
    pointMarkersVisible: false,
    pointMarkersRadius: 3,
    getLineColor: (lineId: string) => `hsl(${Math.abs(lineId.split('').reduce((a, b) => (a << 5) - a + b.charCodeAt(0), 0)) % 360}, 70%, 50%)`,
} as const;

// TODO: This is AI generated code, it works fine visually, but need a thorough review
export class MultilineSeriesPlugin<TData extends MultilineData>
    implements ICustomSeriesPaneView<Time, TData, MultilineSeriesOptions>
{
    _renderer: MultilineSeriesRenderer<TData>;

    constructor() {
        this._renderer = new MultilineSeriesRenderer();
    }

    priceValueBuilder(plotRow: TData): CustomSeriesPricePlotValues {
        // Pre-allocate array for better performance
        const values: number[] = [];
        for (const lineData of Object.values(plotRow.lines)) {
            // Only include valid numeric values
            if (lineData.value !== null && lineData.value !== undefined && !isNaN(lineData.value)) {
                values.push(lineData.value);
            }
        }
        return values;
    }

    isWhitespace(data: TData | WhitespaceData): data is WhitespaceData {
        return (data as Partial<TData>).lines === undefined ||
            Object.keys((data as Partial<TData>).lines || {}).length === 0;
    }

    renderer(): MultilineSeriesRenderer<TData> {
        return this._renderer;
    }

    update(
        data: PaneRendererCustomData<Time, TData>,
        options: MultilineSeriesOptions
    ): void {
        this._renderer.update(data, options);
    }

    defaultOptions() {
        return defaultMultilineOptions;
    }
}

export class MultilineSeriesRenderer<TData extends MultilineData>
    implements ICustomSeriesPaneRenderer
{
    _data: PaneRendererCustomData<Time, TData> | null = null;
    _options: MultilineSeriesOptions | null = null;
    // Cache for color calculations to avoid repeated function calls
    private _colorCache = new Map<string, string>();

    draw(
        target: CanvasRenderingTarget2D,
        priceConverter: PriceToCoordinateConverter
    ): void {
        target.useBitmapCoordinateSpace(scope =>
            this._drawImpl(scope, priceConverter)
        );
    }

    update(
        data: PaneRendererCustomData<Time, TData>,
        options: MultilineSeriesOptions
    ): void {
        this._data = data;
        this._options = options;
        // Clear color cache when options change
        this._colorCache.clear();
    }

    _drawImpl(
        renderingScope: BitmapCoordinatesRenderingScope,
        priceToCoordinate: PriceToCoordinateConverter
    ): void {
        if (
            this._data === null ||
            this._data.bars.length === 0 ||
            this._data.visibleRange === null ||
            this._options === null
        ) {
            return;
        }

        const options = this._options;
        const visibleRange = this._data.visibleRange;
        const bars = this._data.bars;
        const horizontalPixelRatio = renderingScope.horizontalPixelRatio;
        const verticalPixelRatio = renderingScope.verticalPixelRatio;

        // Pre-allocate Map for better performance
        const linePointsMap = new Map<string, ProcessedLinePoint[]>();

        // Single pass through visible bars
        for (let i = visibleRange.from; i < visibleRange.to; i++) {
            const bar = bars[i];
            const x = bar.x * horizontalPixelRatio;
            const lines = bar.originalData.lines;

            // Process each line - use for...in for better performance with objects
            for (const lineId in lines) {
                const lineData = lines[lineId];

                // Skip null, undefined, or NaN values
                if (lineData.value === null || lineData.value === undefined || isNaN(lineData.value)) {
                    continue;
                }

                const y = priceToCoordinate(lineData.value);

                if (y !== null) {
                    // Get or create points array for this line
                    let points = linePointsMap.get(lineId);
                    if (!points) {
                        points = [];
                        linePointsMap.set(lineId, points);
                    }

                    points.push({
                        x: x,
                        y: y * verticalPixelRatio,
                        originalData: lineData
                    });
                }
            }
        }

        // Batch drawing operations
        const ctx = renderingScope.context;
        ctx.save();
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        // Draw all lines in one pass
        for (const [lineId, points] of linePointsMap) {
            if (points.length > 0) {
                this._drawLineOptimized(ctx, points, lineId, options);
            }
        }

        ctx.restore();
    }

    private _drawLineOptimized(
        ctx: CanvasRenderingContext2D,
        points: ProcessedLinePoint[],
        lineId: string,
        options: MultilineSeriesOptions
    ): void {
        // Get cached color or calculate and cache it
        let color = this._colorCache.get(lineId);
        if (!color) {
            color = options.getLineColor(lineId, points[0].originalData);
            this._colorCache.set(lineId, color);
        }

        // Draw line segments, handling gaps in data
        if (points.length >= 2) {
            ctx.strokeStyle = color;
            ctx.lineWidth = options.lineWidth;

            // We need to handle discontinuous data by breaking the line when there are gaps
            // For now, we'll draw as continuous lines, but this could be enhanced
            // to detect gaps in the x-coordinates and break the line accordingly
            ctx.beginPath();
            ctx.moveTo(points[0].x, points[0].y);

            // Use for loop for better performance than forEach
            for (let i = 1; i < points.length; i++) {
                ctx.lineTo(points[i].x, points[i].y);
            }

            ctx.stroke();
        }

        // Draw point markers if enabled - batch these operations too
        if (options.pointMarkersVisible) {
            const radius = options.pointMarkersRadius || 3;
            ctx.fillStyle = color;

            for (let i = 0; i < points.length; i++) {
                const point = points[i];
                ctx.beginPath();
                ctx.arc(point.x, point.y, radius, 0, 2 * Math.PI);
                ctx.fill();
            }
        }
    }
}