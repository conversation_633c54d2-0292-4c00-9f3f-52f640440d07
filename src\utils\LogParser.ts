
const StrategyLogStatusList = [
    "Error",
    "Warn",
    "Success",
    "Default",
    "Debug"
] as const
export type StrategyLogStatus = (typeof StrategyLogStatusList)[number]

export type StrategyLog = {
    status: StrategyLogStatus,
    title: string,
    message: string,
    time: number
}

// TODO: Type safety
export function parseLogs(data: any[]): StrategyLog[] {
    return data.map(arr => ({
        status: StrategyLogStatusList[arr[0]] as StrategyLogStatus,
        title: arr[1] as string,
        message: arr[2] as string,
        time: arr[3] as number
    }))
}