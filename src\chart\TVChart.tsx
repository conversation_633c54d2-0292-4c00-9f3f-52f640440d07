import React, {forwardRef, useCallback, useEffect, useMemo, useRef, useState} from "react";
import {IChartApi, ISeriesApi, LineData, OhlcData, SeriesMarker, Time, UTCTimestamp} from "lightweight-charts";
import {Chart} from "./lib/Chart.tsx";
import {DefaultChartOptions} from "./DefaultOptions.ts";
import {CandleStickSeries, FillSeries, HighlightSeries, LineSeries, MultilineSeries} from "./lib/Series.tsx";
import _ from "lodash";
import {FillData} from "./plugins/FillSeries.ts";
import {
    ChartElementDataItem,
    ChartElementHeaderWithStore,
    checkHeaderWithStoreType
} from "../utils/ChartElementsParser.ts";
import {DataStore} from "../utils/Store.ts";
import {SampleOHLCData} from "./SampleOHLCData.ts";

type TVChartProps = {
    containerKey: React.Key,
    width?: number,
    height?: number,
    stores: ChartElementHeaderWithStore[],
    ohlcStore?: DataStore<ChartElementDataItem["ohlc"]>
}

type Element<T> = { name: string, data: T }

export type TVChartHandle = {
    setOHLCData: (data: OhlcData[]) => void,
    updateOHLCData: (data: OhlcData) => void,
    updateMarkers: (data: SeriesMarker<Time>[]) => void,
    updateMarker: (data: SeriesMarker<Time>) => void,
    setLine: (lineData: Element<LineData[]>) => void,
    updateLine: (data: Element<LineData>) => void,
    setFill: (lineData: Element<FillData[]>) => void,
    updateFill: (data: Element<FillData>) => void,
};

export const TVChart = forwardRef<TVChartHandle, TVChartProps>((props) => {

    const [chartOptions, setChartOptions] = useState(DefaultChartOptions);

    const chartRef = useRef<IChartApi>();
    const handleChartRef = useCallback((r: IChartApi | undefined) => {
        console.log("Chart ref", r)
        chartRef.current = r
    }, []);

    const ohlcSeriesRef = useRef<ISeriesApi<"Candlestick"> | null>(null);
    const handleOhlcSeriesRef = useCallback((r: ISeriesApi<"Candlestick"> | null) => {
        ohlcSeriesRef.current = r
        if (r){
            const store = props.ohlcStore;
            const markerStores: ChartElementHeaderWithStore<"marker">[] = props.stores.filter(s => checkHeaderWithStoreType("marker", s))
            store?.subscribe("TODO", (dt) => {
                r.setData(dt)
            })
            store?.subscribeUpdate("TODO", (dt) => {
                r.update(dt)
            })
            markerStores.forEach(s => {
                s.optionsStore.subscribe(s.name, (opts) => {
                    if (opts.isVisible){
                        s.store.subscribe(s.name, (dt) => {
                            const prevMarkers = [...r.markers()];
                            dt.forEach(newMarker => {
                                if (newMarker.value) {
                                    // Add or update
                                    const existingIndex = prevMarkers.findIndex(m => m.time === newMarker.time);
                                    if (existingIndex !== -1) {
                                        prevMarkers[existingIndex] = newMarker;
                                    } else {
                                        prevMarkers.push(newMarker);
                                    }
                                } else {
                                    // Remove
                                    const index = prevMarkers.findIndex(m => m.time === newMarker.time);
                                    if (index !== -1) {
                                        prevMarkers.splice(index, 1);
                                    }
                                }
                            });
                            r.setMarkers(_.sortBy(prevMarkers, "time"))
                        })
                        s.store.subscribeUpdate(s.name, (newMarker) => {
                            const prevMarkers = [...r.markers()];
                            if (newMarker.value) {
                                // Add or update
                                const existingIndex = prevMarkers.findIndex(m => m.time === newMarker.time);
                                if (existingIndex !== -1) {
                                    prevMarkers[existingIndex] = newMarker;
                                } else {
                                    prevMarkers.push(newMarker);
                                }
                            } else {
                                // Remove
                                const index = prevMarkers.findIndex(m => m.time === newMarker.time);
                                if (index !== -1) {
                                    prevMarkers.splice(index, 1);
                                }
                            }
                            r.setMarkers(_.sortBy(prevMarkers, "time"))
                        })
                    }
                    else {
                        s.store.unsubscribe(s.name)
                        s.store.unsubscribeUpdate(s.name)
                        r.setMarkers((r.markers() as ChartElementDataItem["marker"][]).filter(m => m.name !== s.name))
                    }
                })
            })
        }
    }, [props.stores, props.ohlcStore]);

    // Height and width option updates
    useEffect(() => {
        setChartOptions(opts => ({
            ...opts,
            height: props.height ?? opts.height,
            width: props.width ?? opts.width,
        }))
    }, [props.height, props.width]);

    const fillElements = useMemo(() => {
        const fillHeaders: ChartElementHeaderWithStore<"fill">[] = props.stores.filter(s => checkHeaderWithStoreType("fill", s));
        return fillHeaders.map((header) => (
            <FillSeries
                key={header.name}
                options={{
                    fillColor: header.staticOpts.fillColor,
                    fillOpacity: header.staticOpts.fillOpacity,
                }}
                ref={(r) => {
                    if (r) {
                        console.log("Received Fill Series ref", r)
                        header.store.subscribe(header.name, (dt) => {
                            r.setData(dt)
                        });
                        header.store.subscribeUpdate(header.name, (dt) => {
                            r.update(dt)
                        })
                        header.optionsStore.subscribe(header.name, (dt) => {
                            r.applyOptions({
                                // @ts-expect-error solve later
                                fillColor: dt.fillColor,
                                fillOpacity: dt.fillOpacity,
                                isVisible: dt.isVisible
                            })
                        });
                    }
                }}
            />
        ));
    }, [props.stores]);

    const highlightElements = useMemo(() => {
        const highlightHeaders: ChartElementHeaderWithStore<"highlight">[] = props.stores.filter(s => checkHeaderWithStoreType("highlight", s));
        return highlightHeaders.map((header) => (
            <HighlightSeries
                key={header.name}
                options={{
                    highlightColor: header.staticOpts.highlightColor,
                    highlightOpacity: header.staticOpts.highlightOpacity,
                }}
                ref={(r) => {
                    if (r) {
                        console.log("Received Highlight Series ref", r)
                        header.store.subscribe(header.name, (dt) => {
                            r.setData(dt)
                        });
                        header.store.subscribeUpdate(header.name, (dt) => {
                            r.update(dt)
                        })
                        header.optionsStore.subscribe(header.name, (dt) => {
                            r.applyOptions({
                                // @ts-expect-error solve later
                                highlightColor: dt.highlightColor,
                                highlightOpacity: dt.highlightOpacity,
                                isVisible: dt.isVisible
                            })
                        });
                    }
                }}
            />
        ));
    }, [props.stores]);

    const lineElements = useMemo(() => {
        const lineHeaders: ChartElementHeaderWithStore<"line">[] = props.stores.filter(s => checkHeaderWithStoreType("line", s));
        return lineHeaders.map((header) => (
            <LineSeries
                key={header.name}
                ref={(r) => {
                    if (r) {
                        console.log("Received Line Series ref", r)
                        header.store.subscribe(header.name, (dt) => {
                            r.setData(dt)
                        });
                        header.store.subscribeUpdate(header.name, (dt) => {
                            r.update(dt)
                        })
                        header.optionsStore.subscribe(header.name, (opts) => {
                            r.applyOptions({
                                color: applyOpacity(opts.lineColor, opts.lineOpacity),
                                lineVisible: opts.isVisible,
                                crosshairMarkerVisible: opts.isVisible,
                                priceLineVisible: false,
                                lastValueVisible: false,
                            });
                        })
                    }
                }}
            />
        ));
    }, [props.stores]);

    const multilineElements = useMemo(() => {
        const multilineHeaders: ChartElementHeaderWithStore<"multiline">[] = props.stores.filter(s => checkHeaderWithStoreType<"multiline">("multiline", s));
        return multilineHeaders.map((header) => (
            <MultilineSeries
                key={header.name}
                ref={(r) => {
                    if (r) {
                        console.log("Received Multiline Series ref", r)
                        header.store.subscribe(header.name, (dt) => {
                            const grouped = _.groupBy(dt, 'time');
                            const multilineData = Object.entries(grouped).map(([time, items]) => ({
                                time: Number(time) as UTCTimestamp,
                                lines: _.keyBy(
                                    items.map(({ id, value, time }) => ({ id, value, time })),
                                    'id'
                                )
                            }));
                            r.setData(multilineData);
                        });
                        header.store.subscribeUpdate(header.name, (dt) => {
                            const grouped = _.groupBy([dt], 'time');
                            const multilineData = Object.entries(grouped).map(([time, items]) => ({
                                time: Number(time) as UTCTimestamp,
                                lines: _.keyBy(
                                    items.map(({ id, value, time }) => ({ id, value, time })),
                                    'id'
                                )
                            }));
                            r.update(multilineData[0])
                        })
                    }
                }}
            />
        ));
    }, [props.stores]);

    useEffect(() => {
        return () => {
            props.stores.forEach((h) => {
                h.store.unsubscribe(h.name)
                h.store.unsubscribeUpdate(h.name)
                h.optionsStore.unsubscribe(h.name);

                if (checkHeaderWithStoreType("marker", h)){
                    const r = ohlcSeriesRef.current
                    if (r){
                        r.setMarkers((r.markers() as ChartElementDataItem["marker"][]).filter(m => m.name !== h.name))
                    }
                }
            })
        }
    }, [props.stores]);
    useEffect(() => {
        return () => {
            props.ohlcStore?.unsubscribe("TODO")
            props.ohlcStore?.unsubscribeUpdate("TODO")
        }
    }, [props.ohlcStore]);


    return (
        <div key={props.containerKey} className="absolute">
            <Chart
                key={`chart_${props.containerKey}`}
                ref={handleChartRef}
                className={`h-full flex`}
                options={chartOptions}>

                <CandleStickSeries ref={handleOhlcSeriesRef} data={SampleOHLCData}/>
                {
                    lineElements
                }
                {
                    multilineElements
                }
                {
                    fillElements
                }
                {
                    highlightElements
                }
            </Chart>
        </div>
    )
})

function applyOpacity(color?: string, opacity?: number): string {
    // Return default transparent color if both inputs are undefined
    if (color === undefined && opacity === undefined) {
        return 'rgba(0, 0, 0, 0)';
    }

    // Validate inputs if provided
    if (color === undefined) {
        throw new Error('Color must be provided if opacity is specified');
    }
    if (opacity !== undefined && (opacity < 0 || opacity > 1)) {
        throw new Error('Opacity must be between 0 and 1');
    }

    // Use default opacity of 1 if not provided
    const finalOpacity = opacity !== undefined ? opacity : 1;

    // Handle hex color
    if (color.startsWith('#')) {
        let hex = color.replace('#', '');
        if (hex.length === 3) {
            hex = hex.split('').map(c => c + c).join('');
        }
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);
        return `rgba(${r}, ${g}, ${b}, ${finalOpacity})`;
    }

    // Handle RGB or RGBA color
    if (color.startsWith('rgb')) {
        const matches = color.match(/\d+\.?\d*/g);
        if (!matches) {
            throw new Error('Invalid color format');
        }
        const [r, g, b] = matches;
        return `rgba(${r}, ${g}, ${b}, ${finalOpacity})`;
    }

    throw new Error('Unsupported color format');
}