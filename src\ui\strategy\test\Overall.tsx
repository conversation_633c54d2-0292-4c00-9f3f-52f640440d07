import {useEffect, useMemo, useRef, useState} from "react";
import {Checkbox, Splitter, Statistic, Tabs} from "antd";
import _ from "lodash";
import {BaseChart} from "../../../chart/BaseChart.tsx";
import {BacktestApiResponse} from "../../../data/BacktestOld.ts";
import {emptyChartElements} from "../../../utils/ChartElements.ts";
import {excludeChartElementsByGroup, mergeChartElements, transformChartElements} from "../../../utils/Transformer.ts";
import {useRealtime} from "../../../data/BacktestNew.ts";
import {CustomInputHandle, TVChart} from "../../../chart/BaseChartNew.tsx";

type OverallTabProps = {
    backtestApiResponse?: BacktestApiResponse
}

export function OverallTab({backtestApiResponse}: OverallTabProps){
    const stores = useRealtime();
    const chartRef = useRef<CustomInputHandle>(null);

    // useEffect(() => {
    //     Object.values(stores.ohlc)[0]?.subscribe("main", (dt) => {
    //         console.log("data ohlc", dt)
    //         chartRef.current?.setOhlcData(dt)
    //     })
    //     Object.values(stores.line).forEach((s, idx) => {
    //         s.subscribe(`line:${idx}`, (dt) => {
    //             console.log(`data line:${idx}: ${dt}`)
    //             chartRef.current?.setLines({
    //                 data: dt
    //             })
    //         })
    //         s.subscribeUpdate(`line:${idx}`, (dt) => {
    //             chartRef.current?.updateLine( `line:${idx}`,dt)
    //         })
    //     })
    //     Object.values(stores.ohlc)[0]?.subscribeUpdate("main", (dt) => {
    //         console.log("update ohlc", dt)
    //         chartRef.current?.updateOhlc(dt)
    //     })
    //
    //     return () => {
    //         Object.values(stores.ohlc)[0]?.unsubscribe("main")
    //         Object.values(stores.ohlc)[0]?.unsubscribeUpdate("main")
    //         Object.values(stores.line)[0]?.unsubscribe("main")
    //         Object.values(stores.line)[0]?.unsubscribeUpdate("main")
    //     }
    // }, [stores]);

    const [selectedChartElements, setSelectedChartElements] = useState<string[]>([])

    const filteredChartElements = useMemo(() => {
        // return emptyChartElements();
        if (!backtestApiResponse?.chartElements || !backtestApiResponse?.actionChartElements) {
            return emptyChartElements()
        }
        const ce = mergeChartElements(backtestApiResponse?.chartElements, backtestApiResponse?.actionChartElements)
        return excludeChartElementsByGroup(ce, ["stats"])
    }, [backtestApiResponse?.chartElements]);

    const elements = useMemo(() => {
        return Object.values(filteredChartElements).map(v => Object.entries(v)).flat()
    }, [filteredChartElements]);

    useEffect(() => {
        const selected = elements.filter(([, v]) => v.isVisible).map(([k]) => k)
        setSelectedChartElements(selected)
    }, [elements]);


    const chartElements = useMemo(() => {
        return transformChartElements(filteredChartElements, (v, k) => {
            return {
                ...v,
                isVisible: selectedChartElements.includes(k)
            }
        })
    }, [filteredChartElements, selectedChartElements]);

    const ControlTabItems = [
        {
            key: "1",
            label: <span className={"px-2"}>Controls</span>,
            children: (
                <div style={{width: "100%"}}>
                    {
                        elements.map(([k]) => {
                            return <Checkbox checked={selectedChartElements.includes(k)} onChange={(e) => {
                                if (e.target.checked)
                                    setSelectedChartElements([...selectedChartElements, k])
                                else
                                    setSelectedChartElements(selectedChartElements.filter(d => d !== k))
                            }}>{k}</Checkbox>
                        })
                    }
                </div>
            ),
        },
        {
            key: "2",
            label: <span className={"px-2"}>Logs</span>,
            children: <div style={{width: "100%"}}>
                {
                    backtestApiResponse?.logs.map((l, i) =>
                        <div className={i % 2 === 0 ? "bg-zinc-200/30" : "bg-zinc-200/50"}>
                            {`${l.title}: ${l.message}`}
                        </div>
                    )
                }
            </div>,
        },
    ];

    const [chartWidth, setChartWidth] = useState(0);
    const ResultTabItems = [
        {
            key: "1",
            label: <span className={"px-2"}>Overall</span>,
            children: (
                <div style={{width: "100%"}}>
                    <div style={{display: "flex", width: "100%"}} className={"px-2"}>
                        <Statistic
                            valueStyle={{color: "#3f8600"}}
                            className={"p-2 flex-1"}
                            title={"Net Profit"}
                            prefix={backtestApiResponse?.overallPnl ?? 0 > 0 ? "+" : "-"}
                            value={backtestApiResponse?.overallPnl}
                        />
                        <Statistic
                            valueStyle={{color: ""}}
                            className={"p-2 flex-1"}
                            title={"Max Drawdown"}
                            value={backtestApiResponse?.maxDrawdown}
                        />
                    </div>
                    <div style={{display: "flex", width: "100%"}} className={"px-2"}>
                        <Statistic
                            valueStyle={{color: ""}}
                            className={"p-2 flex-1"}
                            title={"Total Closed Trades"}
                            value={backtestApiResponse?.closedTrades}
                        />
                        <Statistic
                            valueStyle={{color: ""}}
                            className={"p-2 flex-1"}
                            title={"Profitable Trades"}
                            value={`${backtestApiResponse?.profitableTrades} / ${backtestApiResponse?.closedTrades}`}
                            suffix={<span className={"text-sm"}>{`${backtestApiResponse?.profitPercent}%`}</span>}
                        />
                    </div>
                    <div style={{display: "flex", width: "100%"}} className={"px-2"}>
                        <Statistic
                            valueStyle={{color: ""}}
                            className={"p-2 flex-1"}
                            title={"Avg. Profit"}
                            value={backtestApiResponse?.avgProfit}
                        />
                        <Statistic
                            valueStyle={{color: ""}}
                            className={"p-2 flex-1"}
                            title={"Avg. Loss"}
                            value={backtestApiResponse?.avgLoss}
                        />
                    </div>
                </div>
            ),
        },
        {
            key: "2",
            label: <span className={"px-2"}>Pnl</span>,
            children: (
                <div style={{width: "100%"}}>
                    <div style={{display: "flex", width: "100%"}} className={"px-2"}>
                        Instrument wise Breakdown
                    </div>
                </div>
            ),
        },
        {
            key: "3",
            label: <span className={"px-2"}>Trades</span>,
            children: (
                <div style={{width: "100%"}}>
                    <div style={{display: "flex", width: "100%"}} className={"px-2"}>
                        Number of trades, List of trades
                    </div>
                </div>
            ),
        },
    ];

    return (
        <div
            /*ref={divRef}*/ style={{width: "100%", height: "100%"}}
                             className="h-full flex-1"
        >
            {/*{backtestApiResponse === undefined ? <Spin/> : (*/}
            <Splitter
                style={{height: "100%"}}
                className={""}
                onResize={(sizes) => setChartWidth(sizes[0])}
            >
                <Splitter.Panel defaultSize={"60%"} min={"30%"} max={"80%"}>
                    <BaseChart
                        key={"diff"}
                        mainChartData={chartElements.ohlc}
                        markers={chartElements.marker}
                        height={undefined}
                        width={chartWidth}
                        lines={chartElements.line}
                        multiline={chartElements.multiline}
                    />
                    {/*<TVChart*/}
                    {/*    key={"diff"}*/}
                    {/*    ref={chartRef}*/}
                    {/*    markers={chartElements.marker}*/}
                    {/*    height={undefined}*/}
                    {/*    width={chartWidth}*/}
                    {/*    lines={chartElements.line}*/}
                    {/*/>*/}
                </Splitter.Panel>
                <Splitter.Panel>
                    <Splitter layout="vertical">
                        <Splitter.Panel
                            style={{overflow: "hidden", scrollbarWidth: "none"}}
                            defaultSize="60%"
                        >
                            <div style={{height: "100%"}} className="">
                                <Tabs
                                    tabBarGutter={16}
                                    defaultActiveKey="1"
                                    items={ResultTabItems}
                                />
                            </div>
                        </Splitter.Panel>
                        <Splitter.Panel collapsible>
                            <Tabs
                                tabBarGutter={16}
                                defaultActiveKey="1"
                                items={ControlTabItems}
                            />
                        </Splitter.Panel>
                    </Splitter>
                </Splitter.Panel>
            </Splitter>
            {/*)}*/}
        </div>
    );
}