import { useState, useEffect, useRef, RefObject } from 'react';

interface ContainerSize {
    width: number;
    height: number;
}

interface UseContainerSizeReturn<T extends HTMLElement> {
    ref: RefObject<T>;
    size: ContainerSize;
}

function useContainerSize<T extends HTMLElement = HTMLDivElement>(): UseContainerSizeReturn<T> {
    const ref = useRef<T>(null);
    const [size, setSize] = useState<ContainerSize>({ width: 0, height: 0 });

    useEffect(() => {
        const element = ref.current;
        if (!element) return;

        // Create ResizeObserver to watch for size changes
        const resizeObserver = new ResizeObserver((entries) => {
            for (const entry of entries) {
                const { width, height } = entry.contentRect;
                setSize({ width, height });
            }
        });

        // Start observing the element
        resizeObserver.observe(element);

        // Set initial size
        const rect = element.getBoundingClientRect();
        setSize({ width: rect.width, height: rect.height });

        // Cleanup observer on unmount
        return () => {
            resizeObserver.unobserve(element);
            resizeObserver.disconnect();
        };
    }, []);

    return { ref, size };
}

export default useContainerSize;

// Example usage:
/*
function MyComponent() {
  const { ref, size } = useContainerSize<HTMLDivElement>();

  return (
    <div>
      <div
        ref={ref}
        style={{
          width: '50%',
          height: '200px',
          background: 'lightblue',
          resize: 'both',
          overflow: 'auto'
        }}
      >
        Resizable container
      </div>
      <p>Width: {size.width}px</p>
      <p>Height: {size.height}px</p>
    </div>
  );
}
*/