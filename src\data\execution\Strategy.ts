import {URL_ENDPOINT_STRATEGY_ALL, URL_ENDPOINT_STRATEGY_BACKTEST, URL_ENDPOINT_STRATEGY_START} from "../../constants";
import _ from "lodash";
import {TCData} from "../StrategyTerminal.ts";
import {TimeSeriesStore} from "../../utils/Store.ts";
import {
    ChartElementDataItem,
    ChartElementHeader,
    ChartElementHeaderWithStore,
    ChartElementsParser
} from "../../utils/ChartElementsParser.ts";
import {MutableValueStore} from "../../utils/ValueStore.ts";

export async function getStrategies() {
    try {
        const response = await fetch(URL_ENDPOINT_STRATEGY_ALL);
        if (!response.ok) {
            return [];
        }

        const strategies: { id: string, name: string, status: 'active' | 'paused' | 'error' }[] = await response.json();
        return strategies;
    } catch (err) {
        if (err instanceof Error && err.name !== 'AbortError') {
            console.error('Fetch error:', err.message);
        }
    }
    console.log("done")
    return [];
}

export async function startStrategy(symbol: string): Promise<boolean> {
    if (_.isEmpty(symbol)) return false;
    try {
        const response = await fetch(URL_ENDPOINT_STRATEGY_START, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                token: symbol,
            }),
        });
        return response.ok
    } catch (err) {
        if (err instanceof Error && err.name !== 'AbortError') {
            console.error('Fetch error:', err.message);
        }
        return false;
    }
}

export async function runBacktest(symbol: string): Promise<TCData> {
    if (_.isEmpty(symbol)) return {};
    try {
        const response = await fetch(URL_ENDPOINT_STRATEGY_BACKTEST, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                token: symbol,
            }),
        });
        if (response.ok) {
            const data = await response.json();

            console.log("dt", data)

            const studyElementStores: ChartElementHeaderWithStore[] = data.config.studyHeaders.map((h: ChartElementHeader) => {
                const store = new TimeSeriesStore<ChartElementDataItem[typeof h.type]>(i => i.time as number);
                return {
                    ...h,
                    store,
                    optionsStore: new MutableValueStore(h.staticOpts)
                }
            });
            const studyElementsParser = new ChartElementsParser(studyElementStores);
            studyElementsParser.parseData(data.studyElements);

            const actionElementStores: ChartElementHeaderWithStore[] = data.config.actionHeaders.map((h: ChartElementHeader) => {
                const store = h.type === "multiline" ?
                        new TimeSeriesStore<ChartElementDataItem["multiline"]>(i => `${i.time}-${i.id}`) :
                        new TimeSeriesStore<ChartElementDataItem[typeof h.type]>(i => i.time as string | number);
                return {
                    ...h,
                    store,
                    optionsStore: new MutableValueStore(h.staticOpts)
                }
            });
            const actionElementsParser = new ChartElementsParser(actionElementStores);
            actionElementsParser.parseData(data.actionElements);

            return {
                strategy: data.config,
                state: "stopped",
                trades: _.keyBy(data.trades, (t) => t.tradeInstanceId),
                ltps: data.ltps,
                logs: data.logs,
                studyElementHeaders: studyElementStores,
                actionElementHeaders: actionElementStores
            }
        }
        else {
            console.error('Fetch error');
            return {};
        }
    } catch (err) {
        if (err instanceof Error && err.name !== 'AbortError') {
            console.error('Fetch error:', err.message);
        }
        return {};
    }
}