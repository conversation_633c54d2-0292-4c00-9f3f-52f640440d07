import {Ticker} from "./Ticker.ts";
import {Instrument} from "./InstrumentType.ts";
import {OrderDetails} from "./OrderDetails.ts";
import {Id, newId} from "../../utils/Id.ts";

export type Target = {
    id: Id,
    ticker: Ticker,
    instrument: Instrument,
    orderDetails: OrderDetails
}

export type ExitTarget = {
    //Id of the target that is referenced
    _ref: Id,
    modifications: OrderDetails
}


// TODO: Use constructors for each sub type
export const Constructors = {
    Target: (): Target => ({
        id: newId(),
        ticker: {
            type: "Chart",
            displayName: "From Chart"
        },
        instrument: {
            type: "Spot",
            value: undefined
        },
        orderDetails: {
            side: "Buy",
            quantity: {
                type: "Lots",
                value: 1
            },
            validity: "Day",
            orderType: {
                type: "Market"
            }
        }
    })
}