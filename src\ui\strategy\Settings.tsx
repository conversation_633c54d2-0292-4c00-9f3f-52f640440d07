import {Card, ConfigProvider, Segmented, Space, TimePicker} from "antd";
import {LabelledControl} from "../../components/LabelledControl.tsx";
import dayjs  from "dayjs";
import {QuantityInput} from "../../components/CascaderNumberInput.tsx";
import {useMainDispatch, useMainSelector} from "../../redux/MainStore.ts";
import {updateQuantity} from "../../redux/strategy/Strategy.ts";

export function SettingsSection(){

    const quantity = useMainSelector(state => state.strategy.config.maxQuantity)
    const dispatch = useMainDispatch();

    return (
        <ConfigProvider theme={{
            components: {
                Button: {
                    textHoverBg: "transparent",
                    colorBgTextActive: "transparent",
                }
            }
        }}>
            <div className="">
                {/*<Desc text="Strategy Settings"/>*/}
                {/*<Typography.Text type={"secondary"}>*/}
                {/*    This section contains configuration related to cutting off trades.*/}
                {/*</Typography.Text>*/}
                <Card>
                    <Space direction="vertical" size={"large"} style={{width: "100%"}}>
                        <LabelledControl title={"Backtest Range"}
                                         description={"Backtest range."}>
                            <Segmented<string>
                                options={['Last month', 'Last 6 months', 'Last year', "Custom"]}
                                onChange={(value) => {
                                    console.log(value); // string
                                }}
                            />
                        </LabelledControl>
                        <LabelledControl title={"Max Quantity"}
                                         description={"The maximum number of quantity that can be traded per ticker in the strategy, applicable to both buy and sell sides."}>
                            <QuantityInput quantity={quantity ?? { type: "Lots", value: 0 }} onChange={(y) => dispatch(updateQuantity(y))}/>
                        </LabelledControl>
                        <LabelledControl title={"Intraday square off"}
                                         description={"Close all open intraday positions at the specified time, regardless of whether exit conditions have been triggered."}>
                            <TimePicker allowClear={false} defaultValue={dayjs('15:15', "HH:mm")} format={"HH:mm"} />
                        </LabelledControl>
                    </Space>
                </Card>
            </div>
        </ConfigProvider>
    )
}

// TODO: Valid Time range: all trades will only be placed within the time range
// TODO: Should place all targets parallel or one by one