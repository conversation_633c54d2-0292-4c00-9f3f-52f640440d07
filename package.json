{"name": "traly-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@heroicons/react": "^2.2.0", "@nivo/bar": "^0.88.0", "@nivo/line": "^0.88.0", "@nivo/pie": "^0.88.0", "@nivo/scatterplot": "^0.88.0", "@observablehq/plot": "^0.6.17", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@reduxjs/toolkit": "^2.5.0", "@xyflow/react": "^12.6.0", "antd": "^5.23.0", "dayjs": "^1.11.13", "immer": "^10.1.1", "lightweight-charts": "^4.2.2", "lodash": "^4.17.21", "lucide-react": "^0.514.0", "nanoid": "^5.0.9", "react": "^18.3.1", "react-color": "^2.19.3", "react-dom": "^18.3.1", "react-infinite-scroller": "^1.2.6", "react-redux": "^9.2.0", "react-router": "^7.6.1", "react-use-websocket": "^4.13.0", "recharts": "^2.15.3", "slate": "^0.112.0", "slate-history": "^0.110.3", "slate-react": "^0.112.1", "use-immer": "^0.11.0", "uuid": "^11.0.5", "zod": "^4.0.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/lodash": "^4.17.14", "@types/react": "^18.3.18", "@types/react-color": "^3.0.13", "@types/react-dom": "^18.3.5", "@types/react-infinite-scroller": "^1.2.5", "@types/slate": "^0.47.16", "@types/slate-react": "^0.50.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}