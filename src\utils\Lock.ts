export class SimpleLock {
    private locked;
    private waitQueue: (() => void)[] = [];


    constructor(locked: boolean = true) {
        this.locked = locked;
    }

    lock(): void {
        this.locked = true;
    }

    async acquire(): Promise<void> {
        if (this.locked) {
            return new Promise<void>((resolve) => {
                this.waitQueue.push(resolve);
            });
        } else {
            return Promise.resolve();
        }
    }

    releaseAll(): void {
        // Release all waiting promises
        while (this.waitQueue.length > 0) {
            const next = this.waitQueue.shift()!;
            next();
        }

        // Unlock the current lock
        this.locked = false;
    }

    isLocked(): boolean {
        return this.locked;
    }
}