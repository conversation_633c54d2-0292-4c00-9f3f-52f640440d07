import {BacktestApiResponse} from "../../../data/BacktestOld.ts";
import {useMemo} from "react";
import {ChartItem} from "../../plots/ChartItem.tsx";
import {Typography} from "antd";
import {Plot<PERSON>hart} from "../../plots/Plot.tsx";
import {ResponsivePie} from "@nivo/pie";
import _ from "lodash";

type StatsTabProps = {
    backtestApiResponse?: BacktestApiResponse

}

// TODO: Avg profit, avg.loss labels and price label
// TODO: Y AXIS tick Format in k (thousands)
// TODO: Tooltip, on bar chart, show all trade info
// TODO: Axis labels, legends
// TODO: Bar chart show labels (boolean)
// TODO: Bar chart sort domain (boolean)
// TODO: Color scales

export function StatsTab(props: StatsTabProps) {

    const pnlByNumOfTrade = useMemo(() => {
        let sum = 0
        let peak = sum;
        return props.backtestApiResponse?.closedPositions.map((p, i) => {
            sum += p.cumulativePnl
            peak = Math.max(sum, peak)
            return {
                x: i + 1,
                y: sum,
                pnl: p.cumulativePnl,
                drawdown: -(peak - sum),
                c: "Pnl"
            }
        })
    }, [props.backtestApiResponse]);

    const winningPositions = useMemo(() => {
        return props.backtestApiResponse?.closedPositions.filter(p => p.cumulativePnl > 0).map((p, i) => {
            return {
                ...p,
                i: i + 1,
            }
        })
    }, [props.backtestApiResponse])

    const losingPositions = useMemo(() => {
        return props.backtestApiResponse?.closedPositions.filter(p => p.cumulativePnl <= 0).map((p, i) => {
            return {
                ...p,
                i: i + 1,
            }
        })
    }, [props.backtestApiResponse])

    const tradesByProfitRange = useMemo(() => {
        const groups: Record<string, {range: string, rangeStart: number, count: number}> = {};
        const items = props.backtestApiResponse?.closedPositions ?? []
        const max = _.maxBy(items, "cumulativePnl")?.cumulativePnl ?? 4
        const min = _.minBy(items, "cumulativePnl")?.cumulativePnl ?? -4
        const step = max / 4
        for (const item of items) {
            const lowerBound = Math.floor(item.cumulativePnl / step) * step;
            const upperBound = lowerBound + step;
            const key = `${lowerBound.toFixed(0)} to ${upperBound.toFixed(0)}`;
            const upperBoundToHighestRatio = item.cumulativePnl > 0 ? upperBound / (max  ) : lowerBound / min

            if (!groups[key]) {
                groups[key] = { range: key, rangeStart: lowerBound, count: 0 };
            }
            groups[key] = {
                ...groups[key],
                count: groups[key].count + 1,
                clr: applyOpacity(item.cumulativePnl > 0 ? "#59A14F" : "#E15759", Math.abs(upperBoundToHighestRatio))
                // clr: (groups[key].count + 1) % 2 === 0 ? "green" : "red"
            };
        }

        return Object.entries(groups)
            .sort(([, a], [, b]) => a.rangeStart - b.rangeStart)
            .map(([, items]) => ({ ...items }));
    }, [props.backtestApiResponse]);

    const [maxPnlPerTrade, minPnlPerTrade] = useMemo(() => {
        const  x=  [
         _.maxBy(props.backtestApiResponse?.closedPositions, "cumulativePnl")?.cumulativePnl ?? 0,
         _.minBy(props.backtestApiResponse?.closedPositions, "cumulativePnl")?.cumulativePnl ?? 0
        ]
        console.log("mm", x)
        return x
    }, [props.backtestApiResponse]);

    // const o = useMemo(() => {
    //     return props.backtestApiResponse?.chartElements.line.overallPnl.data?.map(x => ({
    //         time: new Date(x.time),
    //         value: x.value
    //     })) ?? []
    // }, [props.backtestApiResponse]);
    // const u = useMemo(() => {
    //     return props.backtestApiResponse?.chartElements.line.realizedPnl.data?.map(x => ({
    //         time: new Date(x.time),
    //         value: x.value
    //     })) ?? []
    // }, [props.backtestApiResponse]);

    const chartItems: (ChartItem & { title: string, width?: number })[] = useMemo(() => {
        return [
            {
                title: "Drawdown & Pnl Curve",
                data: pnlByNumOfTrade,
                marks: [
                    {type: "marker", options: {value: 0, direction: "y"}},
                    {type: "marker", options: {value: 0, direction: "x"}},
                    // {
                    //     type: "bar",
                    //     options: { x: "x", y: "pnl", fill: (d) => d.pnl > 0 ? "#59A14FAA" : "#E15759AA" }
                    // },
                    {
                        type: "area",
                        options: {x: "x", y2: "drawdown", fill: "#E15759", fillOpacity: 0.6},
                    },
                    {
                        type: "line",
                        options: {x: "x", y: "y", stroke: "c", strokeWidth: 2, curve: "linear"},
                    },
                ],
                gridY: true,
            },
            {
                title: "Winning Trades",
                data: winningPositions,
                marks: [
                    {
                        type: "bar",
                        options: {x: "i", y: "cumulativePnl", fill: "#59A14F"},
                    },
                    {
                        type: "marker",
                        options: {value: props.backtestApiResponse?.avgProfit, direction: "y", strokeDasharray: 6},
                    },
                ],
                gridY: true,
                tickStepX: 10
            },
            {
                title: "Losing Trades",
                data: losingPositions,
                marks: [
                    {
                        type: "bar",
                        options: {x: "i", y: "cumulativePnl", fill: "#E15759"},
                    },
                    {
                        type: "marker",
                        options: {value: props.backtestApiResponse?.avgLoss, direction: "y", strokeDasharray: 6},
                    },
                ],
                gridY: true,
                tickStepX: 10
            },
            {
                title: "Profit range distribution",
                data: tradesByProfitRange,
                marks: [
                    {
                        type: "bar",
                        options: {x: "range", y: "count", fill: "white"},
                    },
                    {
                        type: "bar",
                        options: {x: "range", y: "count", fill: "clr"},
                    },
                    {type: "marker", options: {value: 0, direction: "y"}},
                ],
                gridY: true,
                width: 800,
                x: {
                    domain: tradesByProfitRange.map(m => m.range),
                }
            },
            {
                title: "Pnl vs Time taken",
                data: props.backtestApiResponse?.closedPositions ?? [],
                marks: [
                    {type: "marker", options: {value: 0, direction: "y"}},
                    {type: "marker", options: {value: 0, direction: "x"}},
                    {
                        type: "scatter",
                        options: {
                            fill: (d) => d.cumulativePnl > 0 ? "#59A14FAA" : "#E15759AA",
                            stroke: (d) => d.cumulativePnl > 0 ? "#59A14F" : "#E15759",
                            tip: true,
                            x: "timeTaken",
                            y: "cumulativePnl"
                        }
                    },
                ],
                gridY: true,
                gridX: true,
            }
        ] as (ChartItem & { title: string, width?: number })[]
    }, [losingPositions, pnlByNumOfTrade, props.backtestApiResponse?.avgLoss, props.backtestApiResponse?.avgProfit, winningPositions]);

    return (
        <div className={""}>
            <div>
                <div className={"flex flex-col items-center mb-24 h-[300px]"}>
                    <PieChart response={props.backtestApiResponse}/>
                </div>
                {chartItems.map((ci) =>
                    <div className={"flex flex-col items-center mb-24"}>
                        <Typography.Title level={2}>{ci.title}</Typography.Title>
                        <PlotChart chartItem={ci} width={ci.width ?? 1200}/>
                    </div>
                )}
            </div>
            <div className={"h-20"}></div>
        </div>
    )
}

function PieChart({response}: { response?: BacktestApiResponse }) {
    return (
        <>
            {
                response &&
                <ResponsivePie
                    data={[
                        {
                            "id": "Winning",
                            "label": "Winning",
                            "value": response.profitableTrades,
                            "color": "hsl(133, 70%, 50%)"
                        },
                        {
                            "id": "Losing",
                            "label": "Losing",
                            "value": response.closedTrades - response.profitableTrades,
                            "color": "hsl(69, 70%, 50%)"
                        },
                    ]}
                    margin={{top: 40, right: 80, bottom: 80, left: 80}}
                    innerRadius={0.5}
                    padAngle={0.7}
                    cornerRadius={3}
                    activeOuterRadiusOffset={8}
                    borderWidth={1}
                    borderColor={{
                        from: 'color',
                        modifiers: [
                            [
                                'darker',
                                0.2
                            ]
                        ]
                    }}
                    arcLinkLabelsSkipAngle={10}
                    arcLinkLabelsTextColor="#333333"
                    arcLinkLabelsThickness={2}
                    arcLinkLabelsColor={{from: 'color'}}
                    arcLabelsSkipAngle={10}
                    arcLabelsTextColor={{
                        from: 'color',
                        modifiers: [
                            [
                                'darker',
                                2
                            ]
                        ]
                    }}
                    legends={[
                        {
                            anchor: 'bottom',
                            direction: 'row',
                            justify: false,
                            translateX: 0,
                            translateY: 56,
                            itemsSpacing: 0,
                            itemWidth: 100,
                            itemHeight: 18,
                            itemTextColor: '#999',
                            itemDirection: 'left-to-right',
                            itemOpacity: 1,
                            symbolSize: 18,
                            symbolShape: 'circle',
                            effects: [
                                {
                                    on: 'hover',
                                    style: {
                                        itemTextColor: '#000'
                                    }
                                }
                            ]
                        }
                    ]}
                />
            }
        </>
    )
}

// TODO: Move
function applyOpacity(hex: string, opacity: number, minOpacity: number = 0.6) {
    opacity = Math.max(Math.min(1, opacity), -1)
    if (!hex.startsWith("#")) throw new Error("Invalid hex color");

    let r, g, b;

    if (hex.length === 4) {
        r = parseInt(hex[1] + hex[1], 16);
        g = parseInt(hex[2] + hex[2], 16);
        b = parseInt(hex[3] + hex[3], 16);
    } else if (hex.length === 7) {
        r = parseInt(hex.substring(1, 3), 16);
        g = parseInt(hex.substring(3, 5), 16);
        b = parseInt(hex.substring(5, 7), 16);
    } else {
        throw new Error("Invalid hex color format");
    }

    const scaledOpacity = minOpacity + opacity * (1 - minOpacity);

    // Convert to 8-bit alpha
    const a = Math.round(scaledOpacity * 255);

    const res =`#${r.toString(16).padStart(2, "0")}${g.toString(16).padStart(2, "0")}${b.toString(16).padStart(2, "0")}${a.toString(16).padStart(2, "0")}`;
    console.log("clr", res, opacity)
    return res
}