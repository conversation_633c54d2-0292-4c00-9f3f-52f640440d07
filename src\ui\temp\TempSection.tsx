import {useEffect, useMemo, useState} from "react";
import {ChartElementsData, ChartElementsParserX} from "../../utils/ChartElements.ts";
import {Button, Splitter} from "antd";
import {BaseChart} from "../../chart/BaseChart.tsx";
import _ from "lodash";
import {testData} from "./TempData.ts";

export function TempSection(){
    const [count, setCount] = useState(0)
    const data = useData(count);

    const markers = useMemo(() => {
        return _.sortBy(
            Object.values(data?.chartElements.marker ?? {}).flat(),
            "time"
        )
    }, [data])

    const lines = useMemo(() => {
        return Object.values(data?.chartElements.line ?? {}).map( l =>
            l.map(x => ({time: x.time, value: x.value ?? undefined}))
        )
    }, [data]);

    return (
        <>
            <div
                /*ref={divRef}*/ style={{width: "100%", height: "100vh"}}
                                 className=""
            >
                {/*{backtestApiResponse === undefined ? <Spin/> : (*/}
                <Splitter
                    style={{height: "100%"}}
                    className={"w-full"}
                    // onResize={(sizes) => setChartWidth(sizes[0])}
                >
                    <Splitter.Panel defaultSize={"60%"} min={"30%"} max={"80%"}>
                        <BaseChart
                            mainChartData={data?.chartElements.ohlc.main ?? []}
                            markers={markers}
                            height={500}
                            width={500}
                            // width={chartWidth}
                            // lines={linesNew}
                            lines={[]}
                            lines={lines}/>
                    </Splitter.Panel>
                    <Splitter.Panel defaultSize={"60%"} min={"30%"} max={"80%"}>
                        <Button onClick={() => setCount(count + 1)}>
                            Reload
                        </Button>
                    </Splitter.Panel>
                </Splitter>
                {/*)}*/}
            </div>
        </>
    )
}

type Response = {
    chartElements: ChartElementsData
}

function useData(count: number){
    const [data, setData] = useState<Response | undefined>(undefined) // TODO: Use constructor

    useEffect(() => {
        async function run(){
            const dt = await fetch("http://localhost:8080/lcl", {
                method: "GET",
                // headers: {
                //     "Content-Type": "application/json",
                // },
                // body: JSON.stringify(strategy)
            })
            const data = await dt.json()
            // const data = testData as any
            const parser = new ChartElementsParserX(data.headers);
            const res = parser.parseRows(data.data)
            console.log(data)
            console.log(res)
            // const logs = parseLogs(data.logs)
            setData({
                // profitableTrades: data.profitableTrades,
                // profitPercent: data.profitPercent,
                chartElements: res,
                // closedTrades: data.closedTrades,
                // overallPnl: data.overallPnl,
                // maxDrawdown: data.maxDrawdown,
                // avgProfit: data.avgProfit,
                // avgLoss: data.avgLoss,
                // logs: logs,
            })
        }
        run()
    }, [count]);

    return data;
}

