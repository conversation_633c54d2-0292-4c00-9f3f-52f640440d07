import {Card, ConfigProvider, InputNumber, Space} from "antd";
import {LabelledControl} from "../../components/LabelledControl.tsx";
import {ValueOrPercentInput} from "../../components/CascaderNumberInput.tsx";

export function RiskSection() {
    return (
        <ConfigProvider theme={{
            components: {
                Button: {
                    textHoverBg: "transparent",
                    colorBgTextActive: "transparent",

                }
            }
        }}>
            <div className="">
                {/*<Desc text="Risk Management"/>*/}
                <Card>
                    <Space direction="vertical" size={"large"} style={{width: "100%"}}>
                        <LabelledControl title={"Max Drawdown"}
                                         description={"Stops the strategy, when overall drawdown exceeds the limit."}>
                            <ValueOrPercentInput value={{type: "Value", value:0}} onValueChange={() => {}}/>
                        </LabelledControl>
                        <LabelledControl title={"Max Loss"}
                                         description={"Stops the strategy, when overall loss exceeds the limit."}>
                            <InputNumber placeholder="Not set" keyboard changeOnWheel style={{width: 200}} min={0}/>
                        </LabelledControl>
                    </Space>
                </Card>
            </div>
        </ConfigProvider>
    )
}