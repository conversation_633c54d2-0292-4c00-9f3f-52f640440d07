
type Position = {
    name: string;
    price: number | null;
    quantity: number;
};

type LTPRecord = Record<string, number>;

export function calculateTotalPnL(positions: Position[], ltpRecord: LTPRecord): number {
    // Group positions by name to handle multiple entries for the same instrument
    const positionGroups: Record<string, Position[]> = {};

    positions.forEach(position => {
        // Skip pending positions (price is null)
        if (position.price === null) return;

        if (!positionGroups[position.name]) {
            positionGroups[position.name] = [];
        }
        positionGroups[position.name].push(position);
    });

    let totalPnL = 0;

    Object.entries(positionGroups).forEach(([positionName, positionList]) => {
        let netQuantity = 0;
        let totalCost = 0; // Total cost/proceeds from all transactions

        // Calculate net quantity and total cost
        positionList.forEach(position => {
            netQuantity += position.quantity;
            // For buys (positive qty): cost is positive (money spent)
            // For sells (negative qty): cost is negative (money received)
            totalCost += position.quantity * position.price!;
        });

        if (netQuantity === 0) {
            // Position is closed - realized PnL is negative of total cost
            // (if total cost is positive, we lost money; if negative, we made money)
            const realizedPnL = -totalCost;
            totalPnL += realizedPnL;
        } else {
            // Position is open - calculate unrealized PnL using LTP
            if (ltpRecord[positionName]) {
                // Current market value of the position
                const currentValue = netQuantity * ltpRecord[positionName];
                // PnL = current value - total cost
                const unrealizedPnL = currentValue - totalCost;
                totalPnL += unrealizedPnL;
            }
        }
    });

    return totalPnL;
}