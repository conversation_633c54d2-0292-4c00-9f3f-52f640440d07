import {
    But<PERSON>,
    <PERSON><PERSON>,
    Di<PERSON>r,
    Flex,
    Input,
    InputNumber,
    InputRef,
    Popover,
    Segmented,
    Select,
    Space,
    Typography
} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import {useEffect, useMemo, useRef, useState} from "react";

type TargetSelectorProps = {}

export function TargetSelector(props: TargetSelectorProps) {

    return (
        <>
            <Popover trigger={"click"} placement={"rightTop"} content={<Content/>}>
                <Button icon={<PlusOutlined/>} type={"link"} size={"small"}>Add Target</Button>
            </Popover>
        </>
    )
}

// TODO: separate component for instrument type
// TODO: separate component for expiry
// TODO: Price range validations

function Content() {
    const tickerInputRef = useRef<InputRef>(null);
    const [tickerType, setTickerType] = useState<"Chart" | "Custom">("Chart")
    const [instType, setInstType] = useState<"Spot" | "Future" | "CE" | "PE">("Spot")
    const [strike, setStrike] = useState<number | null>(1)
    const [strikeType, setStrikeType] = useState<"ATM" | "ITM" | "OTM">("ATM")
    const [priceMode, setPriceMode] = useState(false)

    const expiryStyle = useMemo(() => {
        return instType === "Spot" ? {display: "none"} : {}
    }, [instType]);
    const typeStyle = useMemo(() => {
        return instType === "Spot" || instType === "Future" ? {display: "none"} : {}
    }, [instType]);

    useEffect(() => {
        if (tickerType === "Custom")
            tickerInputRef.current?.focus()
    }, [tickerType]);

    return (
        <Space direction={"vertical"} size={"middle"}>
            <div>
                <div>Ticker</div>
                <Space>
                    <Segmented<typeof tickerType>
                        value={tickerType}
                        options={['Chart', 'Custom']}
                        onChange={setTickerType}
                    />
                    <Input ref={tickerInputRef} placeholder={"Search ticker..."} disabled={tickerType === "Chart"}/>
                </Space>
            </div>
            <Space direction={"vertical"}>
                <div>Instrument</div>
                <Segmented
                    value={instType}
                    options={['Spot', 'Future', 'CE', "PE"]}
                    onChange={setInstType}
                />
            </Space>
            {
                instType !== "Spot" &&
                <Space>
                    <Space style={expiryStyle} direction={"vertical"}>
                        <Typography.Text>Expiry</Typography.Text>
                        <InputNumber
                            addonBefore={
                                <Select
                                    defaultValue={"Weekly"}>
                                    <Select.Option key={"Weekly"}>Weekly</Select.Option>
                                    <Select.Option key={"Monthly"}>Monthly</Select.Option>
                                    <Select.Option key={"Quarterly"}>Quarterly</Select.Option>
                                </Select>
                            }
                            parser={parseToNumber}
                            formatter={(value) => {
                                // @ts-expect-error - value can be a string, issue is related to antd component
                                return (value !== undefined && value !== 0 && value !== "0") ? `Next +${value}` : "Next"
                            }}
                            style={{width: "180px"}}
                            keyboard
                            changeOnWheel
                            defaultValue={0}
                            min={0}
                            max={10}
                        />
                    </Space>
                    <Space style={typeStyle} direction={"vertical"}>
                        {/*<Select
                            variant={"borderless"}
                            defaultValue={"Weekly"}>
                            <Select.Option key={"Weekly"}>By Strike</Select.Option>
                            <Select.Option key={"Monthly"}>Price Range</Select.Option>
                            <Select.Option key={"Quarterly"}>Quarterly</Select.Option>
                        </Select>*/}
                        <Typography.Text>{priceMode ? "Price range" : "Strike"}</Typography.Text>
                        {
                            priceMode ?
                                <Space.Compact>
                                    <InputNumber keyboard changeOnWheel placeholder={"from"}/>
                                    <InputNumber keyboard changeOnWheel placeholder={"to"}/>
                                </Space.Compact>
                                :
                                <InputNumber
                                    style={{width: "130px"}}
                                    addonBefore={
                                        <Select value={strikeType} onSelect={setStrikeType}>
                                            <Select.Option key={"ATM"}>ATM</Select.Option>
                                            <Select.Option key={"OTM"}>OTM</Select.Option>
                                            <Select.Option key={"ITM"}>ITM</Select.Option>
                                        </Select>
                                    }
                                    keyboard
                                    changeOnWheel
                                    min={1}
                                    max={20}
                                    value={strike}
                                    onChange={setStrike}
                                    disabled={strikeType === "ATM"}
                                />
                        }
                    </Space>
                </Space>
            }
            {
                instType !== "Spot" && instType !== "Future" &&
                <Flex style={typeStyle} justify={"end"}>
                    <Checkbox checked={priceMode} onChange={(b) => setPriceMode(b.target.checked)}>Use price
                        mode</Checkbox>
                </Flex>
            }
            <Divider style={{margin: 0}}/>
            <Flex justify={"end"}>
                <Button type={"primary"}>Add</Button>
            </Flex>
        </Space>
    )
}

// TODO: Move
export function parseToNumber(input: string | undefined): number {
    if (input === undefined) return 0
    if (input === "Next") {
        return 0; // Special case for "Next"
    }

    const match = input.match(/^Next \+(\d+)$/);
    if (match) {
        return parseInt(match[1], 10); // Convert the captured number to an integer
    }

    return 0
}

// Expiry-> Next (+1,+2,+3), Weekly, Monthly, Quarterly