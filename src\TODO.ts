
export const temp = {};


/* Priority A
* => TimeSeriesStore: create a function subscribeLatest to subscribe only for latest values, not oldest
* => Trades: Actions must display - 1 what positions will be executed, 2 status of those positions (not sent, pending, completed)
* => Positions: Token wise Pnl
* => Orders: List of orders
* => Stats View: Add stats and charts
* => Stats View: Add Profit distribution charts
* */

/*
* Priority B
* MultiLineSeries Impl
*
* */

/*
* Features - Terminal
* == Trades ==
* -> UI - Active/Completed
* -> View Trade Option - display it's related chart elements on the chart
* -> View Trade Option - display it's related statistics
*
* == Logs ==
* -> Group - Add group name to the log, group can be Strategy, Action, Trade, Orders, etc.
* -> Filter - By LogStatus
* -> Filter - By Message, By Title, By Group
* -> UI - Scroll to last visited
*
* == Terminal ==
* -> UI - Should display connection status along with strategy status.
* -> Stop button should be disabled once clicked, until status is returned. If stopped, then disable the button and update status is stopped. If failed, enable stop button.
* */
